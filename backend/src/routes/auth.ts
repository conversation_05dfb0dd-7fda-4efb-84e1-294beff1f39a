/**
 * Authentication Routes
 * 
 * Handles user registration, login, logout, token refresh, and password management.
 * Implements the authentication endpoints specified in the SaaS implementation plan.
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/UserService';
import { JWTService } from '../services/JWTService';
import { EmailService } from '../services/EmailService';
// Schemas are now defined inline as JSON schemas
import { 
  LoginRequest,
  RegisterRequest,
  RefreshTokenRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  EmailVerificationRequest,
  ResendVerificationRequest
} from '../types/auth';

// Helper functions for service instantiation
const getUserService = () => new UserService();
const getJWTService = () => new JWTService();

// Register Google OAuth2 plugin
async function registerGoogleOAuth(fastify: FastifyInstance) {
  const googleClientId = process.env['GOOGLE_CLIENT_ID'];
  const googleClientSecret = process.env['GOOGLE_CLIENT_SECRET'];

  if (!googleClientId || !googleClientSecret) {
    console.warn('⚠️  Google OAuth credentials not configured. Google OAuth will be disabled.');
    return;
  }

  await fastify.register(require('@fastify/oauth2'), {
    name: 'googleOAuth2',
    credentials: {
      client: {
        id: googleClientId,
        secret: googleClientSecret
      },
      auth: {
        authorizeHost: 'https://accounts.google.com',
        authorizePath: '/o/oauth2/v2/auth',
        tokenHost: 'https://www.googleapis.com',
        tokenPath: '/oauth2/v4/token'
      }
    },
    startRedirectPath: '/auth/google',
    callbackUri: process.env['GOOGLE_CALLBACK_URL'] || 'http://localhost:3000/auth/google/callback',
    scope: ['openid', 'profile', 'email']
  });
}

export async function authRoutes(fastify: FastifyInstance) {
  // Register Google OAuth2 plugin
  await registerGoogleOAuth(fastify);

  // =============================================================================
  // REGISTRATION & LOGIN
  // =============================================================================

  /**
   * POST /auth/register
   * User registration with email and password
   */
  fastify.post<{ Body: RegisterRequest }>('/register', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password', 'confirmPassword'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          confirmPassword: { type: 'string' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            user: { type: 'object' },
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: RegisterRequest }>, reply: FastifyReply) => {
    try {
      const { email, password, first_name, last_name } = request.body;

      // Check if user already exists
      const existingUser = await getUserService().getUserByEmail(email);
      if (existingUser) {
        return reply.status(400).send({
          error: 'EMAIL_ALREADY_EXISTS',
          message: 'An account with this email already exists',
          requestId: request.id
        });
      }

      // Create new user
      const user = await getUserService().createUser({
        email,
        password,
        first_name: first_name || null,
        last_name: last_name || null,
        role: 'CUSTOMER'
      });

      // Generate auth tokens
      const authResponse = await getJWTService().generateAuthResponse(
        {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at.toISOString(),
          last_login_at: user.last_login_at ? user.last_login_at.toISOString() : null
        },
        request.headers['user-agent'],
        request.ip
      );

      // Send email verification email
      try {
        const emailService = new EmailService();
        await emailService.sendEmailVerification(user.id, user.email, user.first_name);
      } catch (emailError) {
        // Log email error but don't fail registration
        request.log.error('Failed to send verification email:', emailError);
      }

      request.log.info(`User registered: ${user.email}`);

      // Manually construct response to ensure proper serialization
      const response = {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at.toISOString(),
          last_login_at: user.last_login_at ? user.last_login_at.toISOString() : null
        },
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresIn: authResponse.expiresIn
      };

      return reply.status(201).send(response);

    } catch (error) {
      request.log.error('Registration error:', error);
      return reply.status(500).send({
        error: 'REGISTRATION_FAILED',
        message: 'Registration failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/login
   * User login with email and password
   */
  fastify.post<{ Body: LoginRequest }>('/login', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            user: { type: 'object' },
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        401: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: LoginRequest }>, reply: FastifyReply) => {
    try {
      const { email, password } = request.body;

      // Verify credentials
      const user = await getUserService().verifyPassword(email, password);
      if (!user) {
        return reply.status(401).send({
          error: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password',
          requestId: request.id
        });
      }

      // Check if user is active
      if (!user.is_active) {
        return reply.status(401).send({
          error: 'USER_INACTIVE',
          message: 'Your account has been deactivated. Please contact support.',
          requestId: request.id
        });
      }

      // Update last login
      await getUserService().updateLastLogin(user.id);

      // Generate auth tokens
      const authResponse = await getJWTService().generateAuthResponse(
        {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at.toISOString(),
          last_login_at: user.last_login_at ? user.last_login_at.toISOString() : null
        },
        request.headers['user-agent'],
        request.ip
      );

      request.log.info(`User logged in: ${user.email}`);

      // Manually construct response to ensure proper serialization
      const response = {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name || null,
          last_name: user.last_name || null,
          role: user.role,
          is_active: user.is_active,
          email_verified: user.email_verified,
          created_at: user.created_at.toISOString(),
          last_login_at: user.last_login_at ? user.last_login_at.toISOString() : null
        },
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresIn: authResponse.expiresIn
      };

      return reply.send(response);

    } catch (error) {
      request.log.error('Login error:', error);
      return reply.status(500).send({
        error: 'LOGIN_FAILED',
        message: 'Login failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // TOKEN MANAGEMENT
  // =============================================================================

  /**
   * POST /auth/refresh
   * Refresh access token using refresh token
   */
  fastify.post<{ Body: RefreshTokenRequest }>('/refresh', {
    schema: {
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            accessToken: { type: 'string' },
            refreshToken: { type: 'string' },
            expiresIn: { type: 'number' }
          }
        },
        401: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: RefreshTokenRequest }>, reply: FastifyReply) => {
    try {
      const { refreshToken } = request.body;

      const tokenResponse = await getJWTService().refreshAccessToken(
        refreshToken,
        request.headers['user-agent'],
        request.ip
      );

      if (!tokenResponse) {
        return reply.status(401).send({
          error: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token',
          requestId: request.id
        });
      }

      return reply.send(tokenResponse);

    } catch (error) {
      request.log.error('Token refresh error:', error);
      return reply.status(500).send({
        error: 'TOKEN_REFRESH_FAILED',
        message: 'Token refresh failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/logout
   * Logout user and revoke refresh token
   */
  fastify.post<{ Body: RefreshTokenRequest }>('/logout', {
    schema: {
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: RefreshTokenRequest }>, reply: FastifyReply) => {
    try {
      const { refreshToken } = request.body;

      await getJWTService().revokeRefreshToken(refreshToken, 'User logout');

      return reply.send({
        success: true,
        message: 'Logged out successfully'
      });

    } catch (error) {
      request.log.error('Logout error:', error);
      return reply.status(500).send({
        error: 'LOGOUT_FAILED',
        message: 'Logout failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // PASSWORD MANAGEMENT
  // =============================================================================

  /**
   * POST /auth/forgot-password
   * Request password reset
   */
  fastify.post<{ Body: ForgotPasswordRequest }>('/forgot-password', {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: ForgotPasswordRequest }>, reply: FastifyReply) => {
    try {
      const { email } = request.body;

      // Check if user exists (don't reveal if email exists or not)
      const user = await getUserService().getUserByEmail(email);

      if (user && user.is_active) {
        // Generate password reset token and send email
        const emailService = new EmailService();
        await emailService.sendPasswordReset(user.id, user.email, user.first_name);
        request.log.info(`Password reset requested for: ${email}`);
      }

      // Always return success to prevent email enumeration
      return reply.send({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.'
      });

    } catch (error) {
      request.log.error('Forgot password error:', error);
      console.error('Detailed forgot password error:', error);
      return reply.status(500).send({
        error: 'PASSWORD_RESET_FAILED',
        message: 'Password reset request failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/reset-password
   * Reset password using token
   */
  fastify.post<{ Body: ResetPasswordRequest }>('/reset-password', {
    schema: {
      body: {
        type: 'object',
        required: ['token', 'password'],
        properties: {
          token: { type: 'string' },
          password: { type: 'string', minLength: 8 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
            requestId: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: ResetPasswordRequest }>, reply: FastifyReply) => {
    try {
      const { token, password } = request.body;

      // Verify reset token
      const emailService = new EmailService();
      const tokenResult = await emailService.verifyPasswordResetToken(token);

      if (!tokenResult.success) {
        return reply.status(400).send({
          error: 'INVALID_RESET_TOKEN',
          message: tokenResult.error,
          requestId: request.id
        });
      }

      // Update user password
      const userService = getUserService();
      await userService.updatePassword(tokenResult.userId!, password);

      // Mark token as used
      await emailService.markPasswordResetTokenUsed(token);

      request.log.info(`Password reset completed for user: ${tokenResult.userId}`);

      return reply.send({
        success: true,
        message: 'Password reset successfully'
      });

    } catch (error) {
      request.log.error('Reset password error:', error);
      return reply.status(500).send({
        error: 'PASSWORD_RESET_FAILED',
        message: 'Password reset failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // EMAIL VERIFICATION
  // =============================================================================

  /**
   * POST /auth/verify-email
   * Verify email address using token
   */
  fastify.post<{ Body: EmailVerificationRequest }>('/verify-email', {
    schema: {
      body: {
        type: 'object',
        required: ['token'],
        properties: {
          token: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: EmailVerificationRequest }>, reply: FastifyReply) => {
    try {
      const { token } = request.body;

      const emailService = new EmailService();
      const result = await emailService.verifyEmailToken(token);

      if (!result.success) {
        return reply.status(400).send({
          error: 'EMAIL_VERIFICATION_FAILED',
          message: result.error,
          requestId: request.id
        });
      }

      request.log.info(`Email verified for user: ${result.userId}`);

      return reply.send({
        success: true,
        message: 'Email verified successfully'
      });

    } catch (error) {
      request.log.error('Email verification error:', error);
      return reply.status(500).send({
        error: 'EMAIL_VERIFICATION_FAILED',
        message: 'Email verification failed. Please try again.',
        requestId: request.id
      });
    }
  });

  /**
   * POST /auth/resend-verification
   * Resend email verification
   */
  fastify.post<{ Body: ResendVerificationRequest }>('/resend-verification', {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: ResendVerificationRequest }>, reply: FastifyReply) => {
    try {
      const { email } = request.body;

      // Find user by email
      const user = await getUserService().getUserByEmail(email);
      if (!user) {
        // Don't reveal if email exists or not for security
        return reply.send({
          success: true,
          message: 'If the email exists, a verification email has been sent'
        });
      }

      // Check if email is already verified
      if (user.email_verified) {
        return reply.status(400).send({
          error: 'EMAIL_ALREADY_VERIFIED',
          message: 'Email address is already verified',
          requestId: request.id
        });
      }

      // Send verification email
      const emailService = new EmailService();
      await emailService.sendEmailVerification(user.id, user.email, user.first_name);

      request.log.info(`Verification email resent to: ${email}`);

      return reply.send({
        success: true,
        message: 'Verification email sent successfully'
      });

    } catch (error) {
      request.log.error('Resend verification error:', error);
      console.error('Detailed resend verification error:', error);
      return reply.status(500).send({
        error: 'RESEND_VERIFICATION_FAILED',
        message: 'Resend verification failed. Please try again.',
        requestId: request.id
      });
    }
  });

  // =============================================================================
  // GOOGLE OAUTH INTEGRATION
  // =============================================================================

  // Google OAuth callback handler
  fastify.get('/google/callback', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const userService = getUserService();
      const jwtService = getJWTService();

      // Get the access token from Google
      const { token } = await (fastify as any).googleOAuth2.getAccessTokenFromAuthorizationCodeFlow(request);

      // Fetch user info from Google
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${token.access_token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user info from Google');
      }

      const googleUser = await response.json();

      // Check if user exists by email
      let user = await userService.getUserByEmail(googleUser.email);

      if (!user) {
        // Create new user from Google profile
        const createUserData = {
          email: googleUser.email,
          password: null, // No password for OAuth users
          first_name: googleUser.given_name || null,
          last_name: googleUser.family_name || null,
          email_verified: true, // Google emails are pre-verified
          google_id: googleUser.id,
          google_email: googleUser.email
        };

        user = await userService.createUser(createUserData);
      } else {
        // Update existing user with OAuth info if not already set
        if (!user.google_id) {
          await userService.updateUser(user.id, {
            google_id: googleUser.id,
            google_email: googleUser.email,
            email_verified: true
          });
        }
      }

      // Generate JWT tokens and auth response
      const userProfile = {
        id: user.id,
        email: user.email,
        first_name: user.first_name || null,
        last_name: user.last_name || null,
        role: user.role,
        is_active: user.is_active,
        email_verified: user.email_verified,
        created_at: user.created_at,
        last_login_at: user.last_login_at || null
      };

      const authResponse = await jwtService.generateAuthResponse(
        userProfile,
        request.headers['user-agent'],
        request.ip
      );

      // Return success response with tokens
      return reply.send(authResponse);

    } catch (error) {
      request.log.error('Google OAuth callback error:', error);
      return reply.status(500).send({
        error: 'OAUTH_CALLBACK_FAILED',
        message: 'Google OAuth authentication failed. Please try again.',
        requestId: request.id
      });
    }
  });
}
