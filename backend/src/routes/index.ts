import { FastifyPluginAsync } from 'fastify';
import { healthRoutes } from './health';
import { terminalRoutes } from './terminals';
import { metricsRoutes } from './metrics';
import { trackingRoutes } from './tracking';
import { authRoutes } from './auth';
import { userRoutes } from './users';
import { subscriptionRoutes } from './subscriptions';
import { apiKeyRoutes } from './apiKeys';
import { analyticsRoutes } from './analytics';
import { adminRoutes } from './admin';
import { webhookRoutes } from './webhooks';
import { validateEnvironment, RATE_LIMITING_CONFIG } from '../config';
import { authenticateApiKey } from '../middleware/auth';
import { UsageTrackingMiddleware } from '../middleware/usageTracking';

export const registerRoutes: FastifyPluginAsync = async (fastify) => {
  // Initialize usage tracking middleware
  const usageTracking = new UsageTrackingMiddleware();
  await usageTracking.register(fastify);

  // Health route (no authentication required)
  await fastify.register(healthRoutes, { prefix: '/api/v1' });

  // SaaS Authentication routes (no authentication required for auth endpoints)
  await fastify.register(authRoutes, { prefix: '/api/v1/auth' });

  // SaaS User management routes (JWT authentication required)
  await fastify.register(userRoutes, { prefix: '/api/v1/users' });

  // SaaS Subscription management routes (JWT authentication required)
  await fastify.register(subscriptionRoutes, { prefix: '/api/v1/subscriptions' });

  // SaaS API Key management routes (JWT authentication required)
  await fastify.register(apiKeyRoutes, { prefix: '/api/v1' });

  // Analytics routes (JWT authentication required)
  await fastify.register(analyticsRoutes, { prefix: '/api/v1/analytics' });

  // Admin routes (JWT authentication + ADMIN role required)
  await fastify.register(adminRoutes, { prefix: '/api/v1/admin' });

  // Webhook routes (no authentication, signature verification)
  await fastify.register(webhookRoutes, { prefix: '/webhooks' });

  // Protected API routes (API key authentication required)
  await fastify.register(metricsRoutes, { prefix: '/api/v1' });
  await fastify.register(trackingRoutes, { prefix: '/api/v1' });
  await fastify.register(terminalRoutes, { prefix: '/api/v1' });
  
  // Root route - minimal information disclosure with rate limiting
  fastify.get('/', {
    config: {
      rateLimit: {
        max: RATE_LIMITING_CONFIG.rootEndpointLimit, // Configurable rate limit for root endpoint
        timeWindow: '1 minute'
      }
    }
  }, async (request) => {
    const env = validateEnvironment();

    // Log access to root endpoint for security monitoring
    console.log(JSON.stringify({
      timestamp: new Date().toISOString(),
      level: 'INFO',
      event: 'ROOT_ENDPOINT_ACCESS',
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      method: request.method,
      url: request.url
    }));

    // Minimal response - only essential information
    return {
      service: 'Postal Terminal API',
      status: 'operational',
      version: env.APP_VERSION,
      documentation: '/api/v1/health', // Point to health endpoint for basic info
      timestamp: new Date().toISOString()
    };
  });

  // Authenticated API documentation endpoint
  fastify.get('/api/v1/info', {
    preHandler: authenticateApiKey
  }, async () => {
    const env = validateEnvironment();
    return {
      name: 'Postal Terminal API',
      version: env.APP_VERSION,
      environment: env.NODE_ENV,
      status: 'operational',
      timestamp: new Date().toISOString(),
      endpoints: {
        health: {
          path: '/api/v1/health',
          method: 'GET',
          auth: false,
          description: 'System health check'
        },
        metrics: {
          path: '/api/v1/metrics',
          method: 'GET',
          auth: true,
          description: 'System metrics and statistics'
        },
        terminals: {
          path: '/api/v1/terminals',
          method: 'GET',
          auth: true,
          description: 'List postal terminals with filtering'
        },
        terminalById: {
          path: '/api/v1/terminals/:id',
          method: 'GET',
          auth: true,
          description: 'Get specific terminal by ID'
        },
        search: {
          path: '/api/v1/terminals/search',
          method: 'GET',
          auth: true,
          description: 'Full-text search terminals'
        },
        nearby: {
          path: '/api/v1/terminals/nearby',
          method: 'GET',
          auth: true,
          description: 'Find terminals within radius'
        },
        track: {
          path: '/api/v1/track',
          method: 'GET',
          auth: true,
          description: 'Track packages across providers'
        }
      },
      authentication: {
        type: 'API Key',
        header: 'Authorization: Bearer <api_key>',
        required: 'All endpoints except /health and /'
      },
      rateLimiting: {
        default: '1000 requests per minute',
        root: '100 requests per minute'
      }
    };
  });
};
