#!/usr/bin/env node

import { config } from 'dotenv';

// Load environment variables from .env.local first, then .env
config({ path: '.env.local' });
config();

import { createDatabasePool, closeDatabasePool } from '../database/connection';
import { UserService } from '../services/UserService';

interface CreateAdminUserOptions {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  force?: boolean;
}

async function createAdminUser(options: CreateAdminUserOptions) {
  const userService = new UserService();

  try {
    // Check if user already exists
    const existingUser = await userService.getUserByEmail(options.email);
    
    if (existingUser && !options.force) {
      console.log(`❌ User with email ${options.email} already exists.`);
      console.log('   Use --force flag to update existing user to admin role.');
      return;
    }

    if (existingUser && options.force) {
      // Update existing user to admin role
      const updatedUser = await userService.updateUser(existingUser.id, {
        role: 'ADMIN',
        is_active: true,
        email_verified: true,
        password: options.password
      });

      console.log('✅ Existing user updated to admin successfully!');
      console.log('');
      console.log('👤 Admin User Details:');
      console.log(`  ID: ${updatedUser?.id}`);
      console.log(`  Email: ${updatedUser?.email}`);
      console.log(`  Name: ${updatedUser?.first_name || 'N/A'} ${updatedUser?.last_name || 'N/A'}`);
      console.log(`  Role: ${updatedUser?.role}`);
      console.log(`  Active: ${updatedUser?.is_active}`);
      console.log(`  Email Verified: ${updatedUser?.email_verified}`);
      console.log(`  Created: ${updatedUser?.created_at}`);
      console.log('');
      console.log('🔑 Login Credentials:');
      console.log(`  Email: ${options.email}`);
      console.log(`  Password: ${options.password}`);
      
      return updatedUser;
    }

    // Create new admin user
    const newUser = await userService.createUser({
      email: options.email,
      password: options.password,
      first_name: options.firstName || null,
      last_name: options.lastName || null,
      role: 'ADMIN'
    });

    // Verify email automatically for admin users
    await userService.verifyEmail(newUser.id);

    console.log('✅ Admin user created successfully!');
    console.log('');
    console.log('👤 Admin User Details:');
    console.log(`  ID: ${newUser.id}`);
    console.log(`  Email: ${newUser.email}`);
    console.log(`  Name: ${newUser.first_name || 'N/A'} ${newUser.last_name || 'N/A'}`);
    console.log(`  Role: ${newUser.role}`);
    console.log(`  Active: ${newUser.is_active}`);
    console.log(`  Email Verified: true (auto-verified)`);
    console.log(`  Created: ${newUser.created_at}`);
    console.log('');
    console.log('🔑 Login Credentials:');
    console.log(`  Email: ${options.email}`);
    console.log(`  Password: ${options.password}`);
    console.log('');
    console.log('💡 Next Steps:');
    console.log('  1. Use these credentials to log in to the admin dashboard');
    console.log('  2. Change the password after first login for security');
    console.log('  3. Configure additional admin users if needed');

    return newUser;

  } catch (error) {
    console.error('❌ Failed to create admin user:', error);
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);

  // Default options
  const options: CreateAdminUserOptions = {
    email: '<EMAIL>',
    password: 'admin123',
    firstName: 'Admin',
    lastName: 'User'
  };

  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const flag = args[i];
    const value = args[i + 1];

    switch (flag) {
      case '--email':
        options.email = value || options.email;
        break;
      case '--password':
        options.password = value || options.password;
        break;
      case '--first-name':
        options.firstName = value || options.firstName;
        break;
      case '--last-name':
        options.lastName = value || options.lastName;
        break;
      case '--force':
        options.force = true;
        i--; // Adjust for single argument
        break;
      case '--help':
      case '-h':
        console.log('Usage: npm run create-admin-user [options]');
        console.log('Options:');
        console.log('  --email <email>        Admin email (default: <EMAIL>)');
        console.log('  --password <password>  Admin password (default: admin123)');
        console.log('  --first-name <name>    First name (default: Admin)');
        console.log('  --last-name <name>     Last name (default: User)');
        console.log('  --force                Update existing user to admin role');
        console.log('  --help, -h             Show this help message');
        console.log('');
        console.log('Examples:');
        console.log('  npm run create-admin-user');
        console.log('  npm run create-admin-user -- --email <EMAIL> --password mypassword');
        console.log('  npm run create-admin-user -- --force');
        process.exit(0);
        break;
      default:
        if (flag && flag.startsWith('--')) {
          console.log(`Unknown flag: ${flag}`);
          console.log('Use --help for usage information');
          process.exit(1);
        }
        i--; // Adjust for single argument
        break;
    }
  }

  try {
    console.log('🔗 Connecting to database...');
    createDatabasePool();

    console.log('👤 Creating admin user...');
    console.log(`📧 Email: ${options.email}`);
    console.log(`👤 Name: ${options.firstName} ${options.lastName}`);
    console.log(`🔒 Password: ${'*'.repeat(options.password.length)}`);
    console.log('');

    await createAdminUser(options);

  } catch (error) {
    console.error('💥 Error:', error);
    process.exit(1);
  } finally {
    await closeDatabasePool();
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down...');
  await closeDatabasePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down...');
  await closeDatabasePool();
  process.exit(0);
});

if (require.main === module) {
  main();
}
