import 'dotenv/config';
import { cpus } from 'os';
import { readFileSync } from 'fs';
import { join } from 'path';
import Fastify from 'fastify';
import cors from '@fastify/cors';
import compress from '@fastify/compress';

import { validateEnvironment } from './config';
import { validateAndLogConfiguration, validateNoDefaultValues } from './config/critical-config-validator';
import { createDatabasePool, closeDatabasePool, checkDatabaseAndPostGISHealth, logPoolStatus } from './database/connection';
import { registerRoutes } from './routes';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { getNotificationService } from './services/notification-service';
import { registerRateLimit } from './middleware/rate-limit';
import { securityHeadersMiddleware } from './middleware/security-headers';
import { securityLoggingMiddleware } from './middleware/security-logger';
import { jwtAuthPlugin } from './middleware/jwtAuth';
import { UsageAlertService } from './services/UsageAlertService';

async function buildServer() {
  // Validate critical configuration first
  validateAndLogConfiguration();
  validateNoDefaultValues();

  const env = validateEnvironment();
  
  // Create Fastify instance with logging
  const fastify = Fastify({
    logger: {
      level: env.LOG_LEVEL,
      ...(env.NODE_ENV === 'development' && {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'HH:MM:ss Z',
            ignore: 'pid,hostname'
          }
        }
      })
    },
    // Trust proxy for Cloudflare and reverse proxies
    trustProxy: env.NODE_ENV === 'production'
  });

  // Set up notification service with logger
  const notificationService = getNotificationService();
  notificationService.setLogger(fastify.log);

  // Register plugins
  await fastify.register(cors, {
    origin: env.NODE_ENV === 'production' ? false : true,
    credentials: true
  });

  await fastify.register(compress, {
    global: true,
    encodings: ['gzip', 'deflate']
  });

  // Add raw body support for webhooks
  fastify.addContentTypeParser('application/json', { parseAs: 'string' }, (req, body, done) => {
    try {
      const json = JSON.parse(body as string);
      (req as any).rawBody = body;
      done(null, json);
    } catch (err) {
      done(err as Error, undefined);
    }
  });

  // Register rate limiting plugin
  await registerRateLimit(fastify);

  // Register security headers middleware
  await fastify.register(securityHeadersMiddleware);

  // Register security logging middleware
  await fastify.register(securityLoggingMiddleware);

  // Register JWT authentication plugin
  await fastify.register(jwtAuthPlugin);

  // Register middleware
  await fastify.register(requestLogger);
  await fastify.register(errorHandler);

  // Register routes
  await fastify.register(registerRoutes);

  // Start background tasks (e.g., cache cleanup)
  const { startApiCacheCleanup } = await import('./services/cache-cleanup');
  startApiCacheCleanup(fastify.log);

  return fastify;
}

async function startServer() {
  let fastify: Awaited<ReturnType<typeof buildServer>> | null = null;
  
  try {
    const env = validateEnvironment();
    const startTime = Date.now();
    
    // Log startup banner
    logStartupBanner(env);
    
    // Initialize database connection with health checks
    // Use console.log for initial logging before fastify is available
    // eslint-disable-next-line no-console
    console.log('🔗 Initializing database connection...');
    createDatabasePool();
    
    // Verify database connectivity with combined health check
    // eslint-disable-next-line no-console
    console.log('🔍 Checking database and PostGIS health...');
    const healthResults = await checkDatabaseAndPostGISHealth();

    if (!healthResults.database) {
      throw new Error('Database health check failed');
    }

    if (!healthResults.postGIS) {
      throw new Error('PostGIS health check failed');
    }

    // eslint-disable-next-line no-console
    console.log('✅ Database and PostGIS are healthy');

    // Log initial pool status
    logPoolStatus();
    
    // Build server
    // eslint-disable-next-line no-console
    console.log('🚀 Building server...');
    fastify = await buildServer();
    
    // Start listening
    const port = parseInt(env.PORT);
    const host = env.NODE_ENV === 'production' ? '0.0.0.0' : '127.0.0.1';
    
    await fastify.listen({ port, host });

    // Initialize usage alert monitoring system
    // eslint-disable-next-line no-console
    console.log('📊 Starting usage alert monitoring system...');
    const alertService = new UsageAlertService();
    alertService.startMonitoring();

    const startupTime = Date.now() - startTime;

    // Log successful startup
    logSuccessfulStartup(env, port, host, startupTime);
    
    // Set up graceful shutdown after successful startup
    setupGracefulShutdown(fastify, alertService);
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    if (fastify) {
      fastify.log.error({ error: errorMessage }, '❌ Server startup failed');
    } else {
      // eslint-disable-next-line no-console
      console.error('❌ Server startup failed:', errorMessage);
    }
    
    // Notify admin of startup failure
    try {
      const notificationService = getNotificationService();
      await notificationService.notifyDatabaseIssue('server_startup', errorMessage);
    } catch (notificationError) {
      // eslint-disable-next-line no-console
      console.error('Failed to send notification:', notificationError);
    }
    
    process.exit(1);
  }
}

function logStartupBanner(env: ReturnType<typeof validateEnvironment>) {
  const packageJson = JSON.parse(
    readFileSync(join(__dirname, '../package.json'), 'utf-8')
  );
  
  // eslint-disable-next-line no-console
  console.log(`
╔═══════════════════════════════════════════════════════════════════════════════╗
║                           🏢 POSTAL TERMINAL API                              ║
║                                                                               ║
║  Version: ${packageJson.version.padEnd(20)} Environment: ${env.NODE_ENV.padEnd(15)} ║
║  Port: ${env.PORT.padEnd(23)} Log Level: ${env.LOG_LEVEL.padEnd(16)} ║
║                                                                               ║
║  📊 System Information:                                                       ║
║  • Node.js: ${process.version.padEnd(19)} Platform: ${process.platform.padEnd(15)} ║
║  • CPUs: ${cpus().length.toString().padEnd(22)} Architecture: ${process.arch.padEnd(11)} ║
║  • Memory: ${(process.memoryUsage().heapTotal / 1024 / 1024).toFixed(0).padEnd(18)}MB Available          ║
║                                                                               ║
║  🔧 Configuration:                                                            ║
║  • Database: ${env.DATABASE_URL ? 'Configured' : 'Not configured'.padEnd(15)}                              ║
║  • Security: ${env.API_KEY_SECRET ? 'Configured' : 'Not configured'.padEnd(15)}                           ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝
  `);
}

function logSuccessfulStartup(
  env: ReturnType<typeof validateEnvironment>, 
  port: number, 
  host: string, 
  startupTime: number
) {
  // eslint-disable-next-line no-console
  console.log(`
🎉 Server successfully started in ${startupTime}ms

📡 Server URLs:
   • Local:    http://localhost:${port}
   • Network:  http://${host}:${port}
   • Health:   http://localhost:${port}/api/v1/health
   • Metrics:  http://localhost:${port}/api/v1/metrics

📊 Runtime Information:
   • Environment: ${env.NODE_ENV}
   • Log Level: ${env.LOG_LEVEL}
   • Process ID: ${process.pid}
   • Uptime: ${(process.uptime()).toFixed(2)}s

🛡️  Security:
   • API Key Authentication: Enabled
   • CORS: ${env.NODE_ENV === 'development' ? 'Development (permissive)' : 'Production (restricted)'}
   • Compression: Enabled (gzip, deflate)

📝 Ready to serve postal terminal data for Lithuania!
`);
}

// Graceful shutdown handling
function setupGracefulShutdown(
  fastify: Awaited<ReturnType<typeof buildServer>>,
  alertService: UsageAlertService
) {
  const shutdown = async (signal: string) => {
    fastify.log.info(`🛑 Received ${signal}, shutting down gracefully...`);

    try {
      // Stop usage alert monitoring
      alertService.stopMonitoring();
      fastify.log.info('📊 Usage alert monitoring stopped');

      // Close server
      await fastify.close();
      fastify.log.info('🔌 Server closed');

      // Close database connections
      await closeDatabasePool();
      fastify.log.info('🔌 Database pool closed');

      fastify.log.info('✅ Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      fastify.log.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
  
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    fastify.log.fatal({ error }, '💥 Uncaught exception');
    process.exit(1);
  });
  
  process.on('unhandledRejection', (reason) => {
    fastify.log.fatal({ reason }, '💥 Unhandled promise rejection');
    process.exit(1);
  });
}

// Start the server
if (require.main === module) {
  startServer().catch((error) => {
    // eslint-disable-next-line no-console
    console.error('💥 Failed to start server:', error);
    process.exit(1);
  });
}

export { buildServer };
