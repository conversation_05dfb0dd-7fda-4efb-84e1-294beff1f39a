{"name": "postal-terminal-api", "version": "2.1.0", "description": "High-performance postal terminal API for Lithuania", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "migrate": "tsx src/scripts/migrate.ts", "migrate:up": "tsx src/scripts/migrate.ts up", "migrate:status": "tsx src/scripts/migrate.ts status", "migrate:rollback": "tsx src/scripts/migrate.ts rollback", "create-api-key": "tsx src/scripts/create-api-key.ts", "create-admin-user": "tsx src/scripts/create-admin-user.ts", "sync-data": "tsx src/scripts/sync-data.ts", "sync-data:force": "ENABLE_SMART_SYNC=false FORCE_DOWNLOAD_ON_SYNC=true tsx src/scripts/sync-data.ts", "preprocess-dpd": "tsx src/scripts/preprocess-dpd.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["postal", "terminal", "api", "lithuania", "fastify", "postgresql", "postgis"], "author": "Postal Terminal API Team", "license": "MIT", "dependencies": {"@fastify/compress": "^6.4.0", "@fastify/cors": "^8.4.0", "@fastify/oauth2": "^7.9.0", "@fastify/rate-limit": "^7.6.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "csv-parse": "^5.5.2", "dotenv": "16.6.1", "exceljs": "^4.4.0", "fastify": "^4.24.3", "fastify-zod": "^1.4.0", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pg": "^8.16.3", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "stripe": "^18.3.0", "zod": "3.25.76"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "18.19.117", "@types/node-cron": "^3.0.11", "@types/pg": "^8.10.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "artillery": "^2.0.3", "eslint": "^8.53.0", "tsx": "^4.1.2", "typescript": "^5.2.2"}, "engines": {"node": ">=22.0.0"}}