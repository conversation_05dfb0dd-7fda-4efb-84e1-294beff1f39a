# AGENT.md - Postal Terminal API

## Commands
**Backend (Node.js/Fastify API):**
- Build: `cd backend && npm run build`
- Dev: `cd backend && npm run dev` (port 3000)
- Lint: `cd backend && npm run lint` / `npm run lint:fix`
- Single test: No test framework currently set up
- Migration: `cd backend && npm run migrate` / `npm run migrate:up`
- Create API key: `cd backend && npm run create-api-key`

**Frontend (Next.js):**
- Build: `cd frontend && npm run build`
- Dev: `cd frontend && npm run dev` (port 3001)
- Lint: `cd frontend && npm run lint`
- Test: `cd frontend && npm test` (Jest/Testing Library setup)

## Architecture
- **Backend**: Fastify API with PostgreSQL+PostGIS database, sub-50ms response times
- **Database**: PostgreSQL with PostGIS extension for geographic data
- **Frontend**: Next.js with React 19, Tailwind CSS, DaisyUI components
- **Key directories**: `backend/src/{routes,database,middleware,services}`, `frontend/app/`
- **APIs**: RESTful postal terminal search API with authentication via API keys or Bearer tokens
- **Data sources**: LP Express, Omniva, DPD, Venipak terminal data (~1900+ terminals total)

## Code Style
- **TypeScript**: Strict mode enabled, prefer explicit types
- **Imports**: Use relative imports for local files, absolute for packages
- **Backend**: ESLint with TypeScript rules, allow `any` in middleware/routes/types, prefer `const`
- **Frontend**: Next.js ESLint config, paths alias `@/*` points to root
- **Naming**: camelCase for variables/functions, PascalCase for types/components
- **Error handling**: Use Fastify error handlers, structured logging with Pino
