import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, User } from './api';

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isHydrated: boolean;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  initialize: () => Promise<void>;
  setUser: (user: User) => void;
  setTokens: (accessToken: string, refreshToken: string) => void;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      accessToken: null,
      refreshToken: null,
      isLoading: false,
      isAuthenticated: false,
      isHydrated: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true });
        try {
          const response = await apiClient.login(email, password);
          const { accessToken, refreshToken, user } = response;

          set({
            user,
            accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
            isHydrated: true,
          });

          // Also store tokens in localStorage for API client
          localStorage.setItem('access_token', accessToken);
          localStorage.setItem('refresh_token', refreshToken);
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (userData) => {
        set({ isLoading: true });
        try {
          const response = await apiClient.register(userData);
          const { accessToken, refreshToken, user } = response;

          set({
            user,
            accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
            isHydrated: true,
          });

          // Also store tokens in localStorage for API client
          localStorage.setItem('access_token', accessToken);
          localStorage.setItem('refresh_token', refreshToken);
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        try {
          await apiClient.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API call failed:', error);
        } finally {
          // Clear tokens from localStorage
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
          });
        }
      },

      refreshAuth: async () => {
        const { refreshToken } = get();
        if (!refreshToken) {
          get().clearAuth();
          return;
        }

        try {
          const response = await apiClient.refreshToken();
          const { accessToken, refreshToken: newRefreshToken } = response;

          set({
            accessToken,
            refreshToken: newRefreshToken,
            isAuthenticated: true,
          });

          // Also store tokens in localStorage for API client
          localStorage.setItem('access_token', accessToken);
          localStorage.setItem('refresh_token', newRefreshToken);
        } catch (error) {
          console.error('Token refresh failed:', error);
          get().clearAuth();
        }
      },

      initialize: async () => {
        if (typeof window === 'undefined') return;

        set({ isLoading: true });

        try {
          const accessToken = localStorage.getItem('access_token');
          const refreshToken = localStorage.getItem('refresh_token');

          if (!accessToken || !refreshToken) {
            set({ isLoading: false, isHydrated: true });
            return;
          }

          // Set tokens in state
          set({
            accessToken,
            refreshToken,
            isAuthenticated: true,
          });

          // Try to get user profile to validate token
          try {
            const user = await apiClient.getProfile() as User;
            set({ user, isLoading: false, isHydrated: true });
          } catch (error) {
            // Token might be expired, try to refresh
            try {
              const response = await apiClient.refreshToken();
              const { accessToken, refreshToken: newRefreshToken } = response;

              set({
                accessToken,
                refreshToken: newRefreshToken
              });

              // Also store tokens in localStorage for API client
              localStorage.setItem('access_token', accessToken);
              localStorage.setItem('refresh_token', newRefreshToken);

              // Try to get user profile again
              const user = await apiClient.getProfile() as User;
              set({ user, isLoading: false, isHydrated: true });
            } catch (refreshError) {
              console.error('Token refresh failed during initialization:', refreshError);
              get().clearAuth();
              set({ isLoading: false, isHydrated: true });
            }
          }
        } catch (error) {
          console.error('Auth initialization failed:', error);
          get().clearAuth();
          set({ isLoading: false, isHydrated: true });
        }
      },

      setUser: (user: User) => {
        set({ user });
      },

      setTokens: (accessToken: string, refreshToken: string) => {
        set({
          accessToken,
          refreshToken,
          isAuthenticated: true,
        });

        // Also store tokens in localStorage for API client
        localStorage.setItem('access_token', accessToken);
        localStorage.setItem('refresh_token', refreshToken);
      },

      clearAuth: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
        });

        // Also clear tokens from localStorage
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);