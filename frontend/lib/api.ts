const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Type definitions for API responses
export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role: 'ADMIN' | 'CUSTOMER';
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  price_eur: number;
  billing_interval: 'monthly' | 'yearly';
  api_requests_per_month: number;
  api_requests_per_minute: number;
  max_api_keys: number;
  features: Record<string, any>;
  is_active: boolean;
  is_public: boolean;
  sort_order: number;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing';
  billing_cycle: 'monthly' | 'yearly';
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  current_period_start: string;
  current_period_end: string;
  trial_start?: string;
  trial_end?: string;
  canceled_at?: string;
  api_requests_used: number;
  api_requests_reset_at: string;
  created_at: string;
  updated_at: string;
  plan?: SubscriptionPlan;
}

export interface ApiKey {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  is_active: boolean;
  rate_limit_per_minute: number;
  rate_limit_per_day: number;
  rate_limit_burst: number;
  total_requests: number;
  requests_this_month: number;
  last_reset_date: string;
  last_used_at?: string;
  created_at: string;
  expires_at?: string;
  allowed_ips?: string[];
  allowed_domains?: string[];
}

export interface UsageStats {
  current_usage: number;
  limit: number;
  percentage_used: number;
  reset_date: string;
  overage: number;
}

export interface DashboardStats {
  total_requests: number;
  requests_this_month: number;
  api_keys_count: number;
  subscription_plan: string;
  remaining_quota: number;
  total_quota: number;
  usage_percentage: number;
  top_endpoints: Array<{
    endpoint: string;
    count: number;
    percentage: number;
  }>;
  recent_activity: Array<{
    timestamp: string;
    endpoint: string;
    status: number;
    response_time: number;
  }>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Terminal types
export interface PostalTerminal {
  id: string;
  name: string;
  city: string;
  address: string;
  postalCode?: string;
  latitude: number;
  longitude: number;
  countryCode: string;
  provider: string;
  terminalType: 'PARCEL_LOCKER' | 'POST_OFFICE' | 'PICKUP_POINT' | 'DELIVERY_POINT' | 'AUTOMATED_STATION';
  metadata?: any;
  isActive: boolean;
  updated: string;
}

export interface TerminalSearchResult extends PostalTerminal {
  relevanceScore: number;
  matchedFields: string[];
}

export interface NearbyTerminalResult extends PostalTerminal {
  distance: number;
}

export interface TerminalListResponse {
  data: PostalTerminal[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta: {
    requestId: string;
    responseTime: number;
    cacheHit: boolean;
  };
}

export interface TerminalSearchResponse {
  data: TerminalSearchResult[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta: {
    requestId: string;
    responseTime: number;
    searchQuery: string;
    cacheHit: boolean;
  };
}

export interface NearbyTerminalsResponse {
  data: NearbyTerminalResult[];
  meta: {
    requestId: string;
    responseTime: number;
    searchCenter: { lat: number; lng: number };
    searchRadius: number;
    cacheHit: boolean;
  };
}

export interface TerminalDetailResponse {
  data: PostalTerminal;
  meta: {
    requestId: string;
    responseTime: number;
    cacheHit: boolean;
  };
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}/api/v1${endpoint}`;
    const token = this.getToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        throw new ApiError(response.status, data.error || data.message || 'An error occurred');
      }

      return data.data || data as T;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, 'Network error');
    }
  }

  // Special request method for paginated responses that preserves the full response structure
  private async requestPaginated<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<PaginatedResponse<T>> {
    const url = `${this.baseURL}/api/v1${endpoint}`;
    const token = this.getToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data: any = await response.json();

      if (!response.ok) {
        throw new ApiError(response.status, data.error || data.message || 'An error occurred');
      }

      // Return the full response structure for paginated endpoints
      return {
        data: data.data || [],
        pagination: data.pagination || { page: 1, limit: 25, total: 0, totalPages: 0, hasNext: false, hasPrev: false }
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, 'Network error');
    }
  }

  // Proxy request method for terminal endpoints (uses Next.js API routes)
  private async proxyRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Use current domain for proxy requests
    const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(response.status, data.error || data.message || 'An error occurred');
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(0, 'Network error');
    }
  }

  private getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('access_token');
  }

  // Auth endpoints
  async login(email: string, password: string) {
    return this.request<{ accessToken: string; refreshToken: string; user: any }>(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      }
    );
  }

  async register(userData: {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    confirmPassword: string;
  }) {
    return this.request<{ accessToken: string; refreshToken: string; user: any }>(
      '/auth/register',
      {
        method: 'POST',
        body: JSON.stringify({
          email: userData.email,
          password: userData.password,
          confirmPassword: userData.confirmPassword,
          first_name: userData.first_name,
          last_name: userData.last_name
        }),
      }
    );
  }

  async forgotPassword(email: string) {
    return this.request('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async resetPassword(token: string, password: string) {
    return this.request('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({ token, password }),
    });
  }

  async verifyEmail(token: string) {
    return this.request('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
  }

  async refreshToken() {
    const refreshToken = typeof window !== 'undefined' ? localStorage.getItem('refresh_token') : null;
    return this.request<{ accessToken: string; refreshToken: string; expiresIn: number }>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  async logout() {
    const refreshToken = typeof window !== 'undefined' ? localStorage.getItem('refresh_token') : null;
    return this.request('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  // User endpoints
  async getProfile() {
    return this.request('/users/me');
  }



  // Subscription endpoints
  async getSubscriptionPlans(includePrivate = false): Promise<SubscriptionPlan[]> {
    const params = includePrivate ? '?includePrivate=true' : '';
    return this.request(`/subscriptions/plans${params}`);
  }

  async getSubscriptionPlan(id: string): Promise<SubscriptionPlan> {
    return this.request(`/subscriptions/plans/${id}`);
  }

  async getUserSubscription(): Promise<UserSubscription> {
    return this.request('/subscriptions/my-subscription');
  }

  async createCheckoutSession(data: {
    planId: string;
    billingCycle?: 'monthly' | 'yearly';
    successUrl: string;
    cancelUrl: string;
  }): Promise<{ sessionId: string; url: string }> {
    return this.request('/subscriptions/checkout', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async changePlan(data: {
    newPlanId: string;
    billingCycle?: 'monthly' | 'yearly';
    successUrl: string;
    cancelUrl: string;
  }): Promise<{ sessionId: string; url: string }> {
    return this.request('/subscriptions/change-plan', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async cancelSubscription(cancelAtPeriodEnd = true): Promise<{
    subscriptionId: string;
    cancelAtPeriodEnd: boolean;
    currentPeriodEnd: string;
  }> {
    return this.request('/subscriptions/cancel', {
      method: 'POST',
      body: JSON.stringify({ cancelAtPeriodEnd }),
    });
  }

  // API Key endpoints
  async getApiKeys(): Promise<ApiKey[]> {
    return this.request('/my-api-keys');
  }

  async getApiKey(id: string): Promise<ApiKey> {
    return this.request(`/my-api-keys/${id}`);
  }

  async createApiKey(data: {
    name: string;
    description?: string;
    allowed_ips?: string[];
    allowed_domains?: string[];
    expires_at?: string;
  }): Promise<{ apiKey: ApiKey; secret: string }> {
    return this.request('/my-api-keys', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateApiKey(id: string, data: {
    name?: string;
    description?: string;
    is_active?: boolean;
    allowed_ips?: string[];
    allowed_domains?: string[];
    expires_at?: string;
  }): Promise<ApiKey> {
    return this.request(`/my-api-keys/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async deleteApiKey(keyId: string): Promise<void> {
    return this.request(`/my-api-keys/${keyId}`, {
      method: 'DELETE',
    });
  }

  async regenerateApiKey(id: string): Promise<{ apiKey: ApiKey; secret: string }> {
    return this.request(`/my-api-keys/${id}/regenerate`, {
      method: 'POST',
    });
  }

  // Analytics endpoints
  async getDashboardStats(): Promise<DashboardStats> {
    return this.request('/analytics/dashboard');
  }

  async getUsageStats(params?: {
    startDate?: string;
    endDate?: string;
    apiKeyId?: string;
    endpoint?: string;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.apiKeyId) searchParams.append('apiKeyId', params.apiKeyId);
    if (params?.endpoint) searchParams.append('endpoint', params.endpoint);

    const query = searchParams.toString();
    return this.request(`/analytics/usage-stats${query ? `?${query}` : ''}`);
  }

  async getTimeSeriesData(params?: {
    startDate?: string;
    endDate?: string;
    apiKeyId?: string;
    interval?: 'hour' | 'day' | 'week';
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.apiKeyId) searchParams.append('apiKeyId', params.apiKeyId);
    if (params?.interval) searchParams.append('interval', params.interval);

    const query = searchParams.toString();
    return this.request(`/analytics/time-series${query ? `?${query}` : ''}`);
  }

  async getTopEndpoints(params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request(`/analytics/top-endpoints${query ? `?${query}` : ''}`);
  }

  async getUsageQuotas(): Promise<UsageStats> {
    return this.request('/analytics/quotas');
  }

  async getUsageAlerts(includeResolved = false): Promise<any> {
    const params = includeResolved ? '?includeResolved=true' : '';
    return this.request(`/analytics/alerts${params}`);
  }

  // Admin endpoints
  async getSystemOverview(): Promise<any> {
    return this.request('/admin/analytics/system-overview');
  }

  async getAdminUsers(params?: {
    page?: number;
    limit?: number;
    role?: 'ADMIN' | 'CUSTOMER';
    status?: 'active' | 'inactive';
    search?: string;
    sortBy?: 'created_at' | 'updated_at' | 'email' | 'last_login_at';
    sortOrder?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<User>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.role) searchParams.append('role', params.role);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.search) searchParams.append('search', params.search);
    if (params?.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    const query = searchParams.toString();
    return this.requestPaginated(`/admin/users${query ? `?${query}` : ''}`);
  }

  async getAdminUser(id: string): Promise<User & {
    subscriptions: UserSubscription[];
    api_keys: ApiKey[];
    usage_stats: any;
  }> {
    return this.request(`/admin/users/${id}`);
  }

  async updateAdminUser(id: string, data: {
    email?: string;
    role?: 'ADMIN' | 'CUSTOMER';
    is_active?: boolean;
    admin_notes?: string;
  }): Promise<User> {
    return this.request(`/admin/users/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async suspendUser(id: string, data: {
    reason: string;
    notes?: string;
  }): Promise<void> {
    return this.request(`/admin/users/${id}/suspend`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getAdminSubscriptions(params?: {
    page?: number;
    limit?: number;
    status?: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing';
    planId?: string;
    userId?: string;
    expiringInDays?: number;
    sortBy?: 'created_at' | 'updated_at' | 'current_period_end' | 'user_email';
    sortOrder?: 'asc' | 'desc';
  }): Promise<PaginatedResponse<UserSubscription & {
    user_email: string;
    user_role: string;
    user_created_at: string;
    plan_name: string;
    price_eur: string;
    currency: string;
    billing_interval: string;
    features: any;
  }>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.planId) searchParams.append('plan_id', params.planId);
    if (params?.userId) searchParams.append('user_id', params.userId);
    if (params?.expiringInDays) searchParams.append('expiring_in_days', params.expiringInDays.toString());
    if (params?.sortBy) searchParams.append('sort_by', params.sortBy);
    if (params?.sortOrder) searchParams.append('sort_order', params.sortOrder);

    const query = searchParams.toString();
    return this.requestPaginated(`/admin/subscriptions${query ? `?${query}` : ''}`);
  }

  async getAdminSubscription(id: string): Promise<UserSubscription & {
    user: User;
    plan: SubscriptionPlan;
    orders: any[];
  }> {
    return this.request(`/admin/subscriptions/${id}`);
  }

  async updateSubscriptionStatus(id: string, data: {
    status: 'active' | 'past_due' | 'canceled' | 'unpaid' | 'trialing';
    reason: string;
    notes?: string;
  }): Promise<UserSubscription> {
    return this.request(`/admin/subscriptions/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async extendSubscription(id: string, data: {
    days: number;
    reason: string;
    notes?: string;
  }): Promise<UserSubscription> {
    return this.request(`/admin/subscriptions/${id}/extend`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getSystemHealth(): Promise<any> {
    return this.request('/admin/system/health');
  }

  async getSystemMetrics(): Promise<any> {
    return this.request('/admin/system/metrics');
  }

  async getSystemSettings(): Promise<any> {
    return this.request('/admin/system/settings');
  }

  // Subscription Plan Management (Admin)

  async createSubscriptionPlan(data: {
    name: string;
    display_name: string;
    description: string;
    price_eur: number;
    currency: string;
    billing_interval: 'monthly' | 'yearly';
    api_requests_per_month: number;
    features: any;
    is_active: boolean;
  }): Promise<SubscriptionPlan> {
    return this.request('/subscriptions/plans', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateSubscriptionPlan(id: string, data: {
    name?: string;
    display_name?: string;
    description?: string;
    price_eur?: number;
    currency?: string;
    billing_interval?: 'monthly' | 'yearly';
    api_requests_per_month?: number;
    features?: any;
    is_active?: boolean;
  }): Promise<SubscriptionPlan> {
    return this.request(`/subscriptions/plans/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteSubscriptionPlan(id: string): Promise<void> {
    return this.request(`/subscriptions/plans/${id}`, {
      method: 'DELETE',
    });
  }

  // Additional Admin Subscription Methods
  async getSubscriptionAuditLog(id: string, params?: {
    page?: number;
    limit?: number;
    change_type?: string;
  }): Promise<PaginatedResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.change_type) searchParams.append('change_type', params.change_type);

    const query = searchParams.toString();
    return this.requestPaginated(`/admin/subscriptions/${id}/audit-log${query ? `?${query}` : ''}`);
  }

  async getExpiringSubscriptions(params?: {
    days?: number;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<UserSubscription>> {
    const searchParams = new URLSearchParams();
    if (params?.days) searchParams.append('days', params.days.toString());
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.requestPaginated(`/admin/subscriptions/expiring${query ? `?${query}` : ''}`);
  }

  async getSubscriptionStats(): Promise<any> {
    return this.request('/admin/subscriptions/stats');
  }

  // System Alerts Management
  async getSystemAlerts(params?: {
    includeResolved?: boolean;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params?.includeResolved) searchParams.append('includeResolved', params.includeResolved.toString());
    if (params?.severity) searchParams.append('severity', params.severity);
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.requestPaginated(`/admin/analytics/alerts${query ? `?${query}` : ''}`);
  }

  async resolveAlert(alertId: string): Promise<void> {
    return this.request(`/admin/analytics/alerts/${alertId}/resolve`, {
      method: 'POST'
    });
  }

  // System Maintenance
  async runDatabaseVacuum(tables?: string[]): Promise<any> {
    return this.request('/admin/system/maintenance/vacuum', {
      method: 'POST',
      body: JSON.stringify({ tables })
    });
  }

  async clearSystemCache(): Promise<any> {
    return this.request('/admin/system/maintenance/cache-clear', {
      method: 'POST'
    });
  }

  async getSystemLogs(params?: {
    level?: 'error' | 'warn' | 'info' | 'debug';
    limit?: number;
    since?: string;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.level) searchParams.append('level', params.level);
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.since) searchParams.append('since', params.since);

    const query = searchParams.toString();
    return this.request(`/admin/system/logs${query ? `?${query}` : ''}`);
  }

  async updateSystemSettings(settings: Record<string, any>): Promise<any> {
    return this.request('/admin/system/settings', {
      method: 'PATCH',
      body: JSON.stringify(settings)
    });
  }



  // Advanced Analytics
  async getAdminAnalyticsUsers(params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());

    const query = searchParams.toString();
    return this.request(`/admin/analytics/users${query ? `?${query}` : ''}`);
  }

  async getAdminAnalyticsApiKeys(params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.offset) searchParams.append('offset', params.offset.toString());

    const query = searchParams.toString();
    return this.request(`/admin/analytics/api-keys${query ? `?${query}` : ''}`);
  }

  async getSystemMetricsDetailed(params?: {
    period?: 'hour' | 'day' | 'week' | 'month';
    limit?: number;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.period) searchParams.append('period', params.period);
    if (params?.limit) searchParams.append('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request(`/admin/analytics/system-metrics${query ? `?${query}` : ''}`);
  }

  async getUsageTrends(params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);

    const query = searchParams.toString();
    return this.request(`/admin/analytics/usage-trends${query ? `?${query}` : ''}`);
  }

  async getUserAnalytics(userId: string, params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.append('startDate', params.startDate);
    if (params?.endDate) searchParams.append('endDate', params.endDate);

    const query = searchParams.toString();
    return this.request(`/admin/analytics/user/${userId}${query ? `?${query}` : ''}`);
  }

  // Profile management endpoints
  async updateProfile(data: {
    first_name?: string;
    last_name?: string;
    email?: string;
  }): Promise<User> {
    return this.request('/profile', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async changePassword(data: {
    current_password: string;
    new_password: string;
  }): Promise<void> {
    return this.request('/profile/change-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async resendVerificationEmail(): Promise<void> {
    return this.request('/profile/resend-verification', {
      method: 'POST',
    });
  }

  // Terminal search endpoints using Next.js API proxy
  async getTerminals(params?: {
    page?: number;
    limit?: number;
    city?: string;
    provider?: string;
    terminalType?: string;
    countryCode?: string;
    sortBy?: 'name' | 'city' | 'provider' | 'updated';
    sortOrder?: 'asc' | 'desc';
  }): Promise<TerminalListResponse> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.city) searchParams.append('city', params.city);
    if (params?.provider) searchParams.append('provider', params.provider);
    if (params?.terminalType) searchParams.append('terminalType', params.terminalType);
    if (params?.countryCode) searchParams.append('countryCode', params.countryCode);
    if (params?.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    const query = searchParams.toString();
    return this.proxyRequest(`/api/terminals${query ? `?${query}` : ''}`);
  }

  async getTerminal(id: string): Promise<TerminalDetailResponse> {
    return this.proxyRequest(`/api/terminals/${encodeURIComponent(id)}`);
  }

  async searchTerminals(params: {
    q: string;
    page?: number;
    limit?: number;
    city?: string;
    provider?: string;
    terminalType?: string;
    countryCode?: string;
    postalCode?: string;
    sortBy?: 'name' | 'city' | 'provider' | 'updated';
    sortOrder?: 'asc' | 'desc';
  }): Promise<TerminalSearchResponse> {
    const searchParams = new URLSearchParams();
    searchParams.append('q', params.q);
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.city) searchParams.append('city', params.city);
    if (params.provider) searchParams.append('provider', params.provider);
    if (params.terminalType) searchParams.append('terminalType', params.terminalType);
    if (params.countryCode) searchParams.append('countryCode', params.countryCode);
    if (params.postalCode) searchParams.append('postalCode', params.postalCode);
    if (params.sortBy) searchParams.append('sortBy', params.sortBy);
    if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

    return this.proxyRequest(`/api/terminals/search?${searchParams.toString()}`);
  }

  async getNearbyTerminals(params: {
    lat: number;
    lng: number;
    radius?: number;
    limit?: number;
    countryCode?: string;
    terminalType?: string;
  }): Promise<NearbyTerminalsResponse> {
    const searchParams = new URLSearchParams();
    searchParams.append('lat', params.lat.toString());
    searchParams.append('lng', params.lng.toString());
    if (params.radius) searchParams.append('radius', params.radius.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.countryCode) searchParams.append('countryCode', params.countryCode);
    if (params.terminalType) searchParams.append('terminalType', params.terminalType);

    return this.proxyRequest(`/api/terminals/nearby?${searchParams.toString()}`);
  }
}

export const apiClient = new ApiClient();
export { ApiError };
export type { ApiResponse };