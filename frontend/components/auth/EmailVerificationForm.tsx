'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations } from '@/lib/translations';
import { apiClient } from '@/lib/api';
import { useRouter } from 'next/navigation';

interface EmailVerificationFormProps {
  token?: string;
  email?: string;
}

export default function EmailVerificationForm({ token, email }: EmailVerificationFormProps) {
  const { t } = useTranslations();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(!!token);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(0);

  // Auto-verify if token is provided
  useEffect(() => {
    if (token) {
      verifyEmail(token);
    }
  }, [token]);

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const verifyEmail = async (verificationToken: string) => {
    setIsVerifying(true);
    setError('');

    try {
      await apiClient.verifyEmail(verificationToken);
      setIsSuccess(true);
    } catch (err: any) {
      setError(err.message || t('auth.errors.verificationFailed'));
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendEmail = async () => {
    if (!email || countdown > 0) return;
    
    setIsLoading(true);
    setError('');

    try {
      await apiClient.resendVerificationEmail(email);
      setCountdown(60); // 60 second cooldown
    } catch (err: any) {
      setError(err.message || t('auth.errors.resendFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinueToLogin = () => {
    router.push('/auth/login');
  };

  // Success state
  if (isSuccess) {
    return (
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body text-center">
          {/* Success Icon */}
          <div className="mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <h2 className="card-title text-2xl font-bold mb-4">
            {t('auth.emailVerification.successTitle')}
          </h2>
          
          <p className="text-base-content/70 mb-6">
            {t('auth.emailVerification.successMessage')}
          </p>
          
          <button 
            onClick={handleContinueToLogin}
            className="btn btn-primary w-full"
          >
            {t('auth.emailVerification.continueToLogin')}
          </button>
        </div>
      </div>
    );
  }

  // Verifying state
  if (isVerifying) {
    return (
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body text-center">
          {/* Loading Icon */}
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
            <span className="loading loading-spinner loading-lg text-primary"></span>
          </div>
          
          <h2 className="card-title text-2xl font-bold mb-4">
            {t('auth.emailVerification.verifyingTitle')}
          </h2>
          
          <p className="text-base-content/70 mb-6">
            {t('auth.emailVerification.verifyingMessage')}
          </p>
          
          {error && (
            <div className="alert alert-error mb-4">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          )}
          
          <div className="space-y-3">
            <Link href="/auth/login" className="btn btn-outline w-full">
              {t('auth.emailVerification.backToLogin')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Default state - waiting for verification
  return (
    <div className="card w-full max-w-md bg-base-100 shadow-xl">
      <div className="card-body text-center">
        {/* Email Icon */}
        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        
        <h2 className="card-title text-2xl font-bold mb-4">
          {t('auth.emailVerification.title')}
        </h2>
        
        <p className="text-base-content/70 mb-6">
          {t('auth.emailVerification.message')}
        </p>
        
        {email && (
          <div className="bg-base-200 p-4 rounded-lg mb-6">
            <p className="text-sm text-base-content/70 mb-2">
              {t('auth.emailVerification.emailSentTo')}
            </p>
            <p className="font-medium">{email}</p>
          </div>
        )}
        
        {error && (
          <div className="alert alert-error mb-4">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{error}</span>
          </div>
        )}
        
        <div className="space-y-3">
          {email && (
            <button
              onClick={handleResendEmail}
              className={`btn btn-outline w-full ${
                isLoading ? 'loading' : ''
              }`}
              disabled={isLoading || countdown > 0}
            >
              {isLoading ? '' : countdown > 0 
                ? `${t('auth.emailVerification.resendIn')} ${countdown}s`
                : t('auth.emailVerification.resendEmail')
              }
            </button>
          )}
          
          <Link href="/auth/login" className="btn btn-primary w-full">
            {t('auth.emailVerification.backToLogin')}
          </Link>
        </div>
        
        {/* Help Section */}
        <div className="bg-base-200 p-4 rounded-lg mt-6">
          <h4 className="font-medium mb-2">
            {t('auth.emailVerification.helpTitle')}
          </h4>
          <ul className="text-sm text-base-content/70 space-y-1 text-left">
            <li>• {t('auth.emailVerification.helpStep1')}</li>
            <li>• {t('auth.emailVerification.helpStep2')}</li>
            <li>• {t('auth.emailVerification.helpStep3')}</li>
          </ul>
        </div>
        
        {/* Contact Support */}
        <div className="text-center mt-4">
          <p className="text-sm text-base-content/70">
            {t('auth.emailVerification.stillNeedHelp')}{' '}
            <Link href="/contact" className="link link-primary">
              {t('auth.emailVerification.contactSupport')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}