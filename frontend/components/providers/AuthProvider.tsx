'use client';

import { useEffect, ReactNode } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/auth-store';

interface AuthProviderProps {
  children: ReactNode;
}

// Routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/admin',
];

// Routes that should redirect to dashboard if user is already authenticated
const AUTH_ROUTES = [
  '/auth/login',
  '/auth/register',
];

export default function AuthProvider({ children }: AuthProviderProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { isAuthenticated, isLoading, isHydrated, user, initialize } = useAuthStore();

  // Initialize auth state on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  // Handle route protection
  useEffect(() => {
    if (isLoading || !isHydrated) return; // Wait for auth state to be determined and hydrated

    const isProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));
    const isAuthRoute = AUTH_ROUTES.some(route => pathname.startsWith(route));

    if (isProtectedRoute && !isAuthenticated) {
      // Redirect to login if trying to access protected route without authentication
      router.push('/auth/login');
      return;
    }

    if (isAuthRoute && isAuthenticated) {
      // Redirect to dashboard if trying to access auth routes while authenticated
      router.push('/dashboard');
      return;
    }

    // Admin route protection
    if (pathname.startsWith('/admin') && isAuthenticated && user?.role !== 'ADMIN') {
      router.push('/dashboard');
      return;
    }
  }, [pathname, isAuthenticated, isLoading, isHydrated, user, router]);

  // Show loading spinner while initializing auth state
  if (isLoading || !isHydrated) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center">
        <div className="text-center">
          <div className="loading loading-spinner loading-lg mb-4"></div>
          <p className="text-base-content/70">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
