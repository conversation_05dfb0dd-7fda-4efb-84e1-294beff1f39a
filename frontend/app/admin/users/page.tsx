'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient, User, PaginatedResponse } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DataTable, { Column } from '@/components/ui/DataTable';
import Modal, { ConfirmModal } from '@/components/ui/Modal';
import { Users, Search, Filter, Edit, Ban, Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

export default function AdminUsersPage() {
  const { user: currentUser } = useAuthStore();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    role: '',
    status: '',
    sortBy: 'created_at',
    sortOrder: 'desc' as 'asc' | 'desc'
  });
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [suspendConfirm, setSuspendConfirm] = useState<{ show: boolean; user: User | null }>({
    show: false,
    user: null
  });
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    fetchUsers();
  }, [currentUser, pagination.page, pagination.limit, filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: PaginatedResponse<User> = await apiClient.getAdminUsers({
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search || undefined,
        role: filters.role as 'ADMIN' | 'CUSTOMER' || undefined,
        status: filters.status as 'active' | 'inactive' || undefined,
        sortBy: filters.sortBy as any,
        sortOrder: filters.sortOrder
      });

      setUsers(response.data);
      setPagination(prev => ({
        ...prev,
        total: response.pagination?.total || response.data?.length || 0
      }));
    } catch (err: any) {
      console.error('Failed to fetch users:', err);
      setError(err.message || 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    setFilters(prev => ({ ...prev, sortBy: key, sortOrder: direction }));
  };

  const handleSuspendUser = async () => {
    if (!suspendConfirm.user) return;

    try {
      setActionLoading(true);
      await apiClient.suspendUser(suspendConfirm.user.id, {
        reason: 'Suspended by administrator',
        notes: 'User suspended from admin panel'
      });
      await fetchUsers();
      setSuspendConfirm({ show: false, user: null });
    } catch (err: any) {
      console.error('Failed to suspend user:', err);
      setError(err.message || 'Failed to suspend user');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateUser = async (userData: Partial<User>) => {
    if (!selectedUser) return;

    try {
      setActionLoading(true);
      await apiClient.updateAdminUser(selectedUser.id, userData);
      await fetchUsers();
      setShowEditModal(false);
      setSelectedUser(null);
    } catch (err: any) {
      console.error('Failed to update user:', err);
      setError(err.message || 'Failed to update user');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (user: User) => {
    if (!user.is_active) {
      return <div className="badge badge-error gap-1"><XCircle className="w-3 h-3" />Suspended</div>;
    }
    if (!user.email_verified) {
      return <div className="badge badge-warning gap-1"><AlertTriangle className="w-3 h-3" />Unverified</div>;
    }
    return <div className="badge badge-success gap-1"><CheckCircle className="w-3 h-3" />Active</div>;
  };

  const getRoleBadge = (role: string) => {
    return role === 'ADMIN' 
      ? <div className="badge badge-primary gap-1"><Shield className="w-3 h-3" />Admin</div>
      : <div className="badge badge-neutral">Customer</div>;
  };

  const columns: Column<User>[] = [
    {
      key: 'email',
      header: 'User',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.first_name && row.last_name && (
            <div className="text-sm opacity-70">{row.first_name} {row.last_name}</div>
          )}
        </div>
      )
    },
    {
      key: 'role',
      header: 'Role',
      render: (value) => getRoleBadge(value)
    },
    {
      key: 'is_active',
      header: 'Status',
      render: (value, row) => getStatusBadge(row)
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'last_login_at',
      header: 'Last Login',
      render: (value) => value ? new Date(value).toLocaleDateString() : 'Never'
    }
  ];

  if (currentUser && currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <Shield className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access user management.</p>
              <a href="/dashboard" className="btn btn-primary">Go to Dashboard</a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">User Management</h1>
            <p className="opacity-70 mt-2">
              Manage user accounts, roles, and permissions
            </p>
          </div>
          
          <div className="stats shadow">
            <div className="stat">
              <div className="stat-figure text-primary">
                <Users className="w-8 h-8" />
              </div>
              <div className="stat-title">Total Users</div>
              <div className="stat-value text-primary">{pagination.total}</div>
            </div>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Role</span>
                </label>
                <select
                  value={filters.role}
                  onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="">All Roles</option>
                  <option value="CUSTOMER">Customer</option>
                  <option value="ADMIN">Admin</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Status</span>
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Suspended</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Sort By</span>
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="created_at">Created Date</option>
                  <option value="email">Email</option>
                  <option value="last_login_at">Last Login</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Order</span>
                </label>
                <select
                  value={filters.sortOrder}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as 'asc' | 'desc' }))}
                  className="select select-bordered"
                >
                  <option value="desc">Newest First</option>
                  <option value="asc">Oldest First</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <DataTable
          data={users || []}
          columns={columns}
          loading={loading}
          searchable
          searchPlaceholder="Search users by email or name..."
          onSearch={handleSearch}
          sortable
          onSort={handleSort}
          pagination={{
            page: pagination.page,
            limit: pagination.limit,
            total: pagination.total,
            onPageChange: (page) => setPagination(prev => ({ ...prev, page })),
            onLimitChange: (limit) => setPagination(prev => ({ ...prev, limit, page: 1 }))
          }}
          actions={(row) => (
            <>
              <button
                className="btn btn-ghost btn-sm"
                onClick={() => {
                  setSelectedUser(row);
                  setShowEditModal(true);
                }}
                title="Edit User"
              >
                <Edit className="w-4 h-4" />
              </button>
              {row.is_active && row.id !== currentUser?.id && (
                <button
                  className="btn btn-ghost btn-sm text-error"
                  onClick={() => setSuspendConfirm({ show: true, user: row })}
                  title="Suspend User"
                >
                  <Ban className="w-4 h-4" />
                </button>
              )}
            </>
          )}
          emptyMessage="No users found matching your criteria"
        />

        {/* Edit User Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedUser(null);
          }}
          title="Edit User"
          size="md"
        >
          {selectedUser && (
            <div className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Email</span>
                </label>
                <input
                  type="email"
                  value={selectedUser.email}
                  className="input input-bordered"
                  disabled
                />
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Role</span>
                </label>
                <select
                  value={selectedUser.role}
                  onChange={(e) => setSelectedUser(prev => prev ? { ...prev, role: e.target.value as 'ADMIN' | 'CUSTOMER' } : null)}
                  className="select select-bordered"
                >
                  <option value="CUSTOMER">Customer</option>
                  <option value="ADMIN">Admin</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label cursor-pointer">
                  <span className="label-text">Active</span>
                  <input
                    type="checkbox"
                    checked={selectedUser.is_active}
                    onChange={(e) => setSelectedUser(prev => prev ? { ...prev, is_active: e.target.checked } : null)}
                    className="checkbox"
                  />
                </label>
              </div>

              <div className="modal-action">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedUser(null);
                  }}
                  className="btn btn-outline"
                  disabled={actionLoading}
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleUpdateUser({
                    role: selectedUser.role,
                    is_active: selectedUser.is_active
                  })}
                  className="btn btn-primary"
                  disabled={actionLoading}
                >
                  {actionLoading && <span className="loading loading-spinner loading-sm"></span>}
                  Update User
                </button>
              </div>
            </div>
          )}
        </Modal>

        {/* Suspend Confirmation Modal */}
        <ConfirmModal
          isOpen={suspendConfirm.show}
          onClose={() => setSuspendConfirm({ show: false, user: null })}
          onConfirm={handleSuspendUser}
          title="Suspend User"
          message={`Are you sure you want to suspend "${suspendConfirm.user?.email}"? They will lose access to their account.`}
          confirmText="Suspend"
          type="error"
          loading={actionLoading}
        />
      </div>
    </DashboardLayout>
  );
}
