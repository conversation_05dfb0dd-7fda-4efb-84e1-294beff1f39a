'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/authStore';
import { apiClient } from '@/lib/api';
import { SubscriptionPlan } from '@/types/subscription';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { DataTable } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, DollarSign, Users, Calendar } from 'lucide-react';

export default function AdminPlansPage() {
  const { user: currentUser } = useAuthStore();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    if (currentUser?.role === 'ADMIN') {
      fetchPlans();
    }
  }, [currentUser]);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.getSubscriptionPlans();
      setPlans(response);
    } catch (err: any) {
      console.error('Failed to fetch plans:', err);
      setError(err.message || 'Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePlan = async (planData: any) => {
    try {
      await apiClient.createSubscriptionPlan(planData);
      setShowCreateModal(false);
      fetchPlans();
    } catch (err: any) {
      console.error('Failed to create plan:', err);
      setError(err.message || 'Failed to create subscription plan');
    }
  };

  const handleUpdatePlan = async (planData: any) => {
    if (!selectedPlan) return;
    
    try {
      await apiClient.updateSubscriptionPlan(selectedPlan.id, planData);
      setShowEditModal(false);
      setSelectedPlan(null);
      fetchPlans();
    } catch (err: any) {
      console.error('Failed to update plan:', err);
      setError(err.message || 'Failed to update subscription plan');
    }
  };

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this subscription plan?')) {
      return;
    }

    try {
      await apiClient.deleteSubscriptionPlan(planId);
      fetchPlans();
    } catch (err: any) {
      console.error('Failed to delete plan:', err);
      setError(err.message || 'Failed to delete subscription plan');
    }
  };

  const formatPrice = (price: number, interval: string) => {
    return `€${price.toFixed(2)}/${interval}`;
  };

  const columns = [
    {
      key: 'display_name',
      header: 'Plan Name',
      render: (value: string, row: SubscriptionPlan) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm opacity-70">{row.name}</div>
        </div>
      )
    },
    {
      key: 'price_eur',
      header: 'Price',
      render: (value: number, row: SubscriptionPlan) => (
        <div className="flex items-center gap-2">
          <DollarSign className="w-4 h-4 text-success" />
          <span className="font-medium">{formatPrice(value, row.billing_interval)}</span>
        </div>
      )
    },
    {
      key: 'api_requests_per_month',
      header: 'API Requests',
      render: (value: number) => (
        <div className="flex items-center gap-2">
          <Users className="w-4 h-4 text-info" />
          <span>{value?.toLocaleString() || 'Unlimited'}</span>
        </div>
      )
    },
    {
      key: 'billing_interval',
      header: 'Billing',
      render: (value: string) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-warning" />
          <span className="capitalize">{value}</span>
        </div>
      )
    },
    {
      key: 'is_active',
      header: 'Status',
      render: (value: boolean) => (
        <div className={`badge ${value ? 'badge-success' : 'badge-error'}`}>
          {value ? 'Active' : 'Inactive'}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, row: SubscriptionPlan) => (
        <div className="flex gap-2">
          <button
            className="btn btn-sm btn-ghost"
            onClick={() => {
              setSelectedPlan(row);
              setShowEditModal(true);
            }}
          >
            <Edit className="w-4 h-4" />
          </button>
          <button
            className="btn btn-sm btn-ghost text-error"
            onClick={() => handleDeletePlan(row.id)}
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      )
    }
  ];

  if (!currentUser) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-96">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </DashboardLayout>
    );
  }

  if (currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access this page.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Subscription Plans</h1>
            <p className="opacity-70">Manage subscription plans and pricing</p>
          </div>
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateModal(true)}
          >
            <Plus className="w-5 h-5" />
            Create Plan
          </button>
        </div>

        {error && (
          <div className="alert alert-error">
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Plans Table */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <DataTable
              data={plans}
              columns={columns}
              loading={loading}
              emptyMessage="No subscription plans found"
            />
          </div>
        </div>
      </div>

      {/* Create Plan Modal */}
      {showCreateModal && (
        <PlanModal
          title="Create Subscription Plan"
          onSave={handleCreatePlan}
          onClose={() => setShowCreateModal(false)}
        />
      )}

      {/* Edit Plan Modal */}
      {showEditModal && selectedPlan && (
        <PlanModal
          title="Edit Subscription Plan"
          plan={selectedPlan}
          onSave={handleUpdatePlan}
          onClose={() => {
            setShowEditModal(false);
            setSelectedPlan(null);
          }}
        />
      )}
    </DashboardLayout>
  );
}

// Plan Modal Component
function PlanModal({ 
  title, 
  plan, 
  onSave, 
  onClose 
}: { 
  title: string; 
  plan?: SubscriptionPlan; 
  onSave: (data: any) => void; 
  onClose: () => void; 
}) {
  const [formData, setFormData] = useState({
    name: plan?.name || '',
    display_name: plan?.display_name || '',
    description: plan?.description || '',
    price_eur: plan?.price_eur || 0,
    currency: plan?.currency || 'EUR',
    billing_interval: plan?.billing_interval || 'monthly',
    api_requests_per_month: plan?.api_requests_per_month || 1000,
    is_active: plan?.is_active ?? true,
    features: plan?.features || {}
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="modal modal-open">
      <div className="modal-box w-11/12 max-w-2xl">
        <h3 className="font-bold text-lg mb-4">{title}</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Plan Name</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text">Display Name</span>
              </label>
              <input
                type="text"
                className="input input-bordered"
                value={formData.display_name}
                onChange={(e) => setFormData({ ...formData, display_name: e.target.value })}
                required
              />
            </div>
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Description</span>
            </label>
            <textarea
              className="textarea textarea-bordered"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Price (EUR)</span>
              </label>
              <input
                type="number"
                step="0.01"
                className="input input-bordered"
                value={formData.price_eur}
                onChange={(e) => setFormData({ ...formData, price_eur: parseFloat(e.target.value) })}
                required
              />
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text">Billing Interval</span>
              </label>
              <select
                className="select select-bordered"
                value={formData.billing_interval}
                onChange={(e) => setFormData({ ...formData, billing_interval: e.target.value as 'monthly' | 'yearly' })}
              >
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text">API Requests/Month</span>
              </label>
              <input
                type="number"
                className="input input-bordered"
                value={formData.api_requests_per_month}
                onChange={(e) => setFormData({ ...formData, api_requests_per_month: parseInt(e.target.value) })}
                required
              />
            </div>
          </div>

          <div className="form-control">
            <label className="cursor-pointer label">
              <span className="label-text">Active Plan</span>
              <input
                type="checkbox"
                className="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              />
            </label>
          </div>

          <div className="modal-action">
            <button type="button" className="btn" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              {plan ? 'Update' : 'Create'} Plan
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
