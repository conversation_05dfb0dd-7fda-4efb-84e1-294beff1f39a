'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCard from '@/components/ui/StatsCard';
import { Users, CreditCard, BarChart3, AlertTriangle, TrendingUp, Activity, Shield } from 'lucide-react';

interface SystemOverview {
  total_users: number;
  active_users: number;
  total_subscriptions: number;
  active_subscriptions: number;
  total_api_requests: number;
  requests_today: number;
  revenue_this_month: number;
  system_health: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
    response_time: number;
  };
  recent_signups: Array<{
    id: string;
    email: string;
    created_at: string;
    plan: string;
  }>;
  top_users: Array<{
    id: string;
    email: string;
    requests_count: number;
    plan: string;
  }>;
}

export default function AdminDashboardPage() {
  const { user } = useAuthStore();
  const [overview, setOverview] = useState<SystemOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect non-admin users
    if (user && user.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    fetchSystemOverview();
  }, [user]);

  const fetchSystemOverview = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch data from multiple endpoints
      const [systemData, usersData, subscriptionsData] = await Promise.all([
        apiClient.getSystemOverview(),
        apiClient.getAdminUsers({ limit: 1 }), // Just get summary
        apiClient.getAdminSubscriptions({ limit: 1 }) // Just get summary
      ]);

      // Combine the data into the expected structure
      const combinedData = {
        // System stats from analytics
        ...systemData,
        // User stats from users endpoint
        total_users: usersData.pagination?.total || 0,
        active_users: usersData.pagination?.total || 0, // All users are considered active for now
        // Subscription stats
        total_subscriptions: subscriptionsData.pagination?.total || 0,
        active_subscriptions: subscriptionsData.pagination?.total || 0,
        // System health (mock for now)
        system_health: {
          status: 'healthy',
          uptime: '99.9%',
          last_check: new Date().toISOString()
        }
      };

      setOverview(combinedData);
    } catch (err: any) {
      console.error('Failed to fetch system overview:', err);
      setError(err.message || 'Failed to load system overview');
    } finally {
      setLoading(false);
    }
  };

  const getHealthBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <div className="badge badge-success">Healthy</div>;
      case 'warning':
        return <div className="badge badge-warning">Warning</div>;
      case 'critical':
        return <div className="badge badge-error">Critical</div>;
      default:
        return <div className="badge badge-neutral">Unknown</div>;
    }
  };

  if (user && user.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <Shield className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access the admin dashboard.</p>
              <a href="/dashboard" className="btn btn-primary">
                Go to Dashboard
              </a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !overview) {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <AlertTriangle className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Failed to Load Admin Dashboard</h1>
              <p className="py-6 opacity-70">{error}</p>
              <button 
                className="btn btn-primary"
                onClick={fetchSystemOverview}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="opacity-70 mt-2">
              System overview and administrative controls
            </p>
          </div>
          
          {/* System Health */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body py-4 px-6">
              <div className="flex items-center gap-3">
                <Activity className="w-5 h-5" />
                <div>
                  <div className="text-sm opacity-70">System Status</div>
                  {loading ? (
                    <div className="skeleton h-4 w-16"></div>
                  ) : (
                    getHealthBadge(overview?.system_health?.status || 'unknown')
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Users"
            value={overview?.total_users?.toLocaleString() || '0'}
            change={overview?.active_users ? {
              value: Math.round((overview.active_users / overview.total_users) * 100),
              type: 'increase'
            } : undefined}
            icon={Users}
            iconColor="text-primary"
            description={`${overview?.active_users || 0} active`}
            loading={loading}
          />

          <StatsCard
            title="Subscriptions"
            value={overview?.total_subscriptions?.toLocaleString() || '0'}
            icon={CreditCard}
            iconColor="text-success"
            description={`${overview?.active_subscriptions || 0} active`}
            loading={loading}
          />

          <StatsCard
            title="API Requests"
            value={overview?.total_api_requests?.toLocaleString() || '0'}
            icon={BarChart3}
            iconColor="text-info"
            description={`${overview?.requests_today?.toLocaleString() || 0} today`}
            loading={loading}
          />

          <StatsCard
            title="Revenue (Month)"
            value={overview?.revenue_this_month ? `€${overview.revenue_this_month.toFixed(2)}` : '€0.00'}
            icon={TrendingUp}
            iconColor="text-warning"
            description="Current month"
            loading={loading}
          />
        </div>

        {/* System Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">System Health</h3>
              {loading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex justify-between">
                      <div className="h-3 bg-base-300 rounded w-1/2"></div>
                      <div className="h-3 bg-base-300 rounded w-1/4"></div>
                    </div>
                  ))}
                </div>
              ) : overview?.system_health ? (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm opacity-70">Status</span>
                    {getHealthBadge(overview.system_health.status)}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm opacity-70">Uptime</span>
                    <span className="font-semibold">{overview.system_health.uptime.toFixed(2)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm opacity-70">Avg Response Time</span>
                    <span className="font-semibold">{overview.system_health.response_time.toFixed(0)}ms</span>
                  </div>
                </div>
              ) : null}
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">Recent Signups</h3>
              {loading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-base-300 rounded-full"></div>
                      <div className="flex-1">
                        <div className="h-3 bg-base-300 rounded w-3/4 mb-1"></div>
                        <div className="h-2 bg-base-300 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : overview?.recent_signups?.length ? (
                <div className="space-y-3">
                  {overview.recent_signups.slice(0, 5).map((signup) => (
                    <div key={signup.id} className="flex items-center gap-3">
                      <div className="avatar placeholder">
                        <div className="bg-primary text-primary-content rounded-full w-8">
                          <span className="text-xs">{signup.email[0].toUpperCase()}</span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{signup.email}</p>
                        <p className="text-xs opacity-70">
                          {new Date(signup.created_at).toLocaleDateString()} • {signup.plan}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 opacity-70">
                  <Users className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">No recent signups</p>
                </div>
              )}
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">Top Users</h3>
              {loading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 bg-base-300 rounded-full"></div>
                        <div className="h-3 bg-base-300 rounded w-20"></div>
                      </div>
                      <div className="h-3 bg-base-300 rounded w-12"></div>
                    </div>
                  ))}
                </div>
              ) : overview?.top_users?.length ? (
                <div className="space-y-3">
                  {overview.top_users.slice(0, 5).map((user) => (
                    <div key={user.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="avatar placeholder">
                          <div className="bg-secondary text-secondary-content rounded-full w-8">
                            <span className="text-xs">{user.email[0].toUpperCase()}</span>
                          </div>
                        </div>
                        <div className="min-w-0">
                          <p className="text-sm font-medium truncate">{user.email}</p>
                          <p className="text-xs opacity-70">{user.plan}</p>
                        </div>
                      </div>
                      <div className="badge badge-neutral badge-sm">
                        {user.requests_count.toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 opacity-70">
                  <BarChart3 className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">No usage data</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h3 className="card-title">Quick Actions</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <a href="/admin/users" className="btn btn-outline">
                <Users className="w-4 h-4 mr-2" />
                Manage Users
              </a>
              <a href="/admin/subscriptions" className="btn btn-outline">
                <CreditCard className="w-4 h-4 mr-2" />
                Subscriptions
              </a>
              <a href="/admin/analytics" className="btn btn-outline">
                <BarChart3 className="w-4 h-4 mr-2" />
                Analytics
              </a>
              <a href="/admin/settings" className="btn btn-outline">
                <Shield className="w-4 h-4 mr-2" />
                Settings
              </a>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
