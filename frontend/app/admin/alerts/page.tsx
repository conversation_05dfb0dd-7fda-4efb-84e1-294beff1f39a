'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { AlertTriangle, CheckCircle, Clock, Filter, RefreshCw, X } from 'lucide-react';

interface SystemAlert {
  id: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  created_at: string;
  resolved_at?: string;
  resolved_by?: string;
  metadata?: any;
}

export default function AdminAlertsPage() {
  const { user: currentUser } = useAuthStore();
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    includeResolved: false,
    severity: '' as '' | 'low' | 'medium' | 'high' | 'critical'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0
  });
  const [resolving, setResolving] = useState<string | null>(null);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    if (currentUser?.role === 'ADMIN') {
      fetchAlerts();
    }
  }, [currentUser, pagination.page, pagination.limit, filters]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.getSystemAlerts({
        includeResolved: filters.includeResolved,
        severity: filters.severity || undefined,
        page: pagination.page,
        limit: pagination.limit
      });

      setAlerts(response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.pagination?.total || 0
      }));
    } catch (err: any) {
      console.error('Failed to fetch alerts:', err);
      setError(err.message || 'Failed to load system alerts');
    } finally {
      setLoading(false);
    }
  };

  const handleResolveAlert = async (alertId: string) => {
    try {
      setResolving(alertId);
      await apiClient.resolveAlert(alertId);
      await fetchAlerts(); // Refresh the list
    } catch (err: any) {
      console.error('Failed to resolve alert:', err);
      setError(err.message || 'Failed to resolve alert');
    } finally {
      setResolving(null);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-error';
      case 'high': return 'text-warning';
      case 'medium': return 'text-info';
      case 'low': return 'text-success';
      default: return 'text-base-content';
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical': return 'badge-error';
      case 'high': return 'badge-warning';
      case 'medium': return 'badge-info';
      case 'low': return 'badge-success';
      default: return 'badge-ghost';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (!currentUser) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-96">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </DashboardLayout>
    );
  }

  if (currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access this page.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">System Alerts</h1>
            <p className="opacity-70">Monitor and manage system alerts and notifications</p>
          </div>
          <button
            className="btn btn-primary"
            onClick={fetchAlerts}
            disabled={loading}
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5" />
                <span className="font-medium">Filters:</span>
              </div>
              
              <div className="form-control">
                <label className="cursor-pointer label">
                  <input
                    type="checkbox"
                    className="checkbox checkbox-primary"
                    checked={filters.includeResolved}
                    onChange={(e) => setFilters({ ...filters, includeResolved: e.target.checked })}
                  />
                  <span className="label-text ml-2">Include Resolved</span>
                </label>
              </div>

              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={filters.severity}
                  onChange={(e) => setFilters({ ...filters, severity: e.target.value as any })}
                >
                  <option value="">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Alerts List */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <span className="loading loading-spinner loading-lg"></span>
              </div>
            ) : alerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-16 h-16 mx-auto text-success mb-4" />
                <h3 className="text-lg font-medium">No Alerts Found</h3>
                <p className="opacity-70">
                  {filters.includeResolved 
                    ? 'No alerts match your current filters.'
                    : 'All systems are running smoothly! No active alerts at this time.'
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`border rounded-lg p-4 ${
                      alert.resolved_at ? 'border-success bg-success/5' : 'border-base-300'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <AlertTriangle className={`w-5 h-5 ${getSeverityColor(alert.severity)}`} />
                          <div className={`badge ${getSeverityBadge(alert.severity)}`}>
                            {alert.severity.toUpperCase()}
                          </div>
                          <span className="text-sm opacity-70">{alert.source}</span>
                          {alert.resolved_at && (
                            <div className="badge badge-success">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Resolved
                            </div>
                          )}
                        </div>
                        
                        <h3 className="font-medium text-lg mb-1">{alert.title}</h3>
                        <p className="opacity-70 mb-3">{alert.message}</p>
                        
                        <div className="flex items-center gap-4 text-sm opacity-60">
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>Created: {formatDate(alert.created_at)}</span>
                          </div>
                          {alert.resolved_at && (
                            <div className="flex items-center gap-1">
                              <CheckCircle className="w-4 h-4" />
                              <span>Resolved: {formatDate(alert.resolved_at)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {!alert.resolved_at && (
                        <button
                          className="btn btn-success btn-sm"
                          onClick={() => handleResolveAlert(alert.id)}
                          disabled={resolving === alert.id}
                        >
                          {resolving === alert.id ? (
                            <span className="loading loading-spinner loading-sm"></span>
                          ) : (
                            <CheckCircle className="w-4 h-4" />
                          )}
                          Resolve
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination.total > pagination.limit && (
              <div className="flex justify-center mt-6">
                <div className="join">
                  <button
                    className="join-item btn"
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={pagination.page === 1 || loading}
                  >
                    Previous
                  </button>
                  <button className="join-item btn btn-active">
                    Page {pagination.page} of {Math.ceil(pagination.total / pagination.limit)}
                  </button>
                  <button
                    className="join-item btn"
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit) || loading}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
