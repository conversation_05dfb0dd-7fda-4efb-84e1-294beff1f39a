'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Key, User, Calendar, Activity, Eye, EyeOff, Trash2, <PERSON><PERSON><PERSON>riangle } from 'lucide-react';

interface ApiKeyWithUser {
  id: string;
  name: string;
  key_preview: string;
  user_id: string;
  user_email: string;
  user_name: string;
  is_active: boolean;
  last_used_at?: string;
  created_at: string;
  expires_at?: string;
  usage_count: number;
  rate_limit_per_minute: number;
}

export default function AdminApiKeysPage() {
  const { user: currentUser } = useAuthStore();
  const [apiKeys, setApiKeys] = useState<ApiKeyWithUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    active: 'all' as 'all' | 'active' | 'inactive',
    search: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0
  });

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    if (currentUser?.role === 'ADMIN') {
      fetchApiKeys();
    }
  }, [currentUser, pagination.page, pagination.limit, filters]);

  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // For now, we'll use mock data since the backend endpoint returns analytics
      // In a real implementation, this would call a dedicated admin API keys endpoint
      const mockApiKeys: ApiKeyWithUser[] = [
        {
          id: '1',
          name: 'Production API Key',
          key_preview: 'pk_live_****1234',
          user_id: 'user1',
          user_email: '<EMAIL>',
          user_name: 'John Doe',
          is_active: true,
          last_used_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          usage_count: 15420,
          rate_limit_per_minute: 100
        },
        {
          id: '2',
          name: 'Development Key',
          key_preview: 'pk_test_****5678',
          user_id: 'user2',
          user_email: '<EMAIL>',
          user_name: 'Jane Smith',
          is_active: true,
          last_used_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          usage_count: 892,
          rate_limit_per_minute: 60
        },
        {
          id: '3',
          name: 'Suspended Key',
          key_preview: 'pk_live_****9999',
          user_id: 'user3',
          user_email: '<EMAIL>',
          user_name: 'Bob Wilson',
          is_active: false,
          last_used_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          usage_count: 5234,
          rate_limit_per_minute: 30
        }
      ];

      // Apply filters
      let filteredKeys = mockApiKeys;
      
      if (filters.active !== 'all') {
        filteredKeys = filteredKeys.filter(key => 
          filters.active === 'active' ? key.is_active : !key.is_active
        );
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredKeys = filteredKeys.filter(key =>
          key.name.toLowerCase().includes(searchLower) ||
          key.user_email.toLowerCase().includes(searchLower) ||
          key.user_name.toLowerCase().includes(searchLower)
        );
      }

      setApiKeys(filteredKeys);
      setPagination(prev => ({
        ...prev,
        total: filteredKeys.length
      }));
    } catch (err: any) {
      console.error('Failed to fetch API keys:', err);
      setError(err.message || 'Failed to load API keys');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleApiKey = async (keyId: string, currentStatus: boolean) => {
    try {
      // In a real implementation, this would call the backend API
      console.log(`${currentStatus ? 'Deactivating' : 'Activating'} API key:`, keyId);
      
      // Update local state
      setApiKeys(prev => prev.map(key => 
        key.id === keyId ? { ...key, is_active: !currentStatus } : key
      ));
    } catch (err: any) {
      console.error('Failed to toggle API key:', err);
      setError(err.message || 'Failed to update API key status');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getLastUsedText = (lastUsedAt?: string) => {
    if (!lastUsedAt) return 'Never';
    
    const now = new Date();
    const lastUsed = new Date(lastUsedAt);
    const diffMs = now.getTime() - lastUsed.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 30) return `${diffDays}d ago`;
    return formatDate(lastUsedAt);
  };

  if (!currentUser) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center min-h-96">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </DashboardLayout>
    );
  }

  if (currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access this page.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">API Key Management</h1>
            <p className="opacity-70">Monitor and manage all API keys across the system</p>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              ×
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="form-control">
                <input
                  type="text"
                  placeholder="Search by name, email, or user..."
                  className="input input-bordered w-full max-w-xs"
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                />
              </div>

              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={filters.active}
                  onChange={(e) => setFilters({ ...filters, active: e.target.value as any })}
                >
                  <option value="all">All Keys</option>
                  <option value="active">Active Only</option>
                  <option value="inactive">Inactive Only</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* API Keys Table */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <span className="loading loading-spinner loading-lg"></span>
              </div>
            ) : apiKeys.length === 0 ? (
              <div className="text-center py-8">
                <Key className="w-16 h-16 mx-auto opacity-50 mb-4" />
                <h3 className="text-lg font-medium">No API Keys Found</h3>
                <p className="opacity-70">No API keys match your current filters.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>API Key</th>
                      <th>User</th>
                      <th>Usage</th>
                      <th>Last Used</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {apiKeys.map((apiKey) => (
                      <tr key={apiKey.id}>
                        <td>
                          <div>
                            <div className="font-medium">{apiKey.name}</div>
                            <div className="text-sm opacity-70 font-mono">{apiKey.key_preview}</div>
                            <div className="text-xs opacity-50">
                              Created: {formatDate(apiKey.created_at)}
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4" />
                            <div>
                              <div className="font-medium">{apiKey.user_name}</div>
                              <div className="text-sm opacity-70">{apiKey.user_email}</div>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center gap-2">
                            <Activity className="w-4 h-4 text-info" />
                            <div>
                              <div className="font-medium">{apiKey.usage_count.toLocaleString()}</div>
                              <div className="text-sm opacity-70">{apiKey.rate_limit_per_minute}/min limit</div>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            <span>{getLastUsedText(apiKey.last_used_at)}</span>
                          </div>
                        </td>
                        <td>
                          <div className={`badge ${apiKey.is_active ? 'badge-success' : 'badge-error'}`}>
                            {apiKey.is_active ? 'Active' : 'Inactive'}
                          </div>
                        </td>
                        <td>
                          <div className="flex gap-2">
                            <button
                              className={`btn btn-sm ${apiKey.is_active ? 'btn-warning' : 'btn-success'}`}
                              onClick={() => handleToggleApiKey(apiKey.id, apiKey.is_active)}
                              title={apiKey.is_active ? 'Deactivate' : 'Activate'}
                            >
                              {apiKey.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
