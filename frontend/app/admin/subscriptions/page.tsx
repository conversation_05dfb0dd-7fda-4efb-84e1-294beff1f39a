'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient, UserSubscription, SubscriptionPlan, User, PaginatedResponse } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DataTable, { Column } from '@/components/ui/DataTable';
import Modal, { ConfirmModal } from '@/components/ui/Modal';
import { CreditCard, Calendar, AlertTriangle, CheckCircle, XCircle, Clock, Shield, Edit } from 'lucide-react';

type SubscriptionWithDetails = UserSubscription & {
  user: User;
  plan: SubscriptionPlan;
};

export default function AdminSubscriptionsPage() {
  const { user: currentUser } = useAuthStore();
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 25,
    total: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    planId: '',
    expiringInDays: '',
    sortBy: 'created_at',
    sortOrder: 'desc' as 'asc' | 'desc'
  });
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithDetails | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);

  useEffect(() => {
    // Redirect non-admin users
    if (currentUser && currentUser.role !== 'ADMIN') {
      window.location.href = '/dashboard';
      return;
    }

    fetchSubscriptions();
    fetchPlans();
  }, [currentUser, pagination.page, pagination.limit, filters]);

  const fetchSubscriptions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: PaginatedResponse<SubscriptionWithDetails> = await apiClient.getAdminSubscriptions({
        page: pagination.page,
        limit: pagination.limit,
        status: filters.status as any || undefined,
        planId: filters.planId || undefined,
        expiringInDays: filters.expiringInDays ? parseInt(filters.expiringInDays) : undefined,
        sortBy: filters.sortBy as any,
        sortOrder: filters.sortOrder
      });

      setSubscriptions(response.data);
      setPagination(prev => ({
        ...prev,
        total: response.pagination?.total || response.data?.length || 0
      }));
    } catch (err: any) {
      console.error('Failed to fetch subscriptions:', err);
      setError(err.message || 'Failed to load subscriptions');
    } finally {
      setLoading(false);
    }
  };

  const fetchPlans = async () => {
    try {
      const plansData = await apiClient.getSubscriptionPlans(true);
      setPlans(plansData);
    } catch (err: any) {
      console.error('Failed to fetch plans:', err);
    }
  };

  const handleUpdateSubscriptionStatus = async (status: string, reason: string) => {
    if (!selectedSubscription) return;

    try {
      setActionLoading(true);
      await apiClient.updateSubscriptionStatus(selectedSubscription.id, {
        status: status as any,
        reason,
        notes: `Status updated by admin: ${currentUser?.email}`
      });
      await fetchSubscriptions();
      setShowEditModal(false);
      setSelectedSubscription(null);
    } catch (err: any) {
      console.error('Failed to update subscription:', err);
      setError(err.message || 'Failed to update subscription');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <div className="badge badge-success gap-1"><CheckCircle className="w-3 h-3" />Active</div>;
      case 'past_due':
        return <div className="badge badge-warning gap-1"><Clock className="w-3 h-3" />Past Due</div>;
      case 'canceled':
        return <div className="badge badge-error gap-1"><XCircle className="w-3 h-3" />Canceled</div>;
      case 'unpaid':
        return <div className="badge badge-error gap-1"><AlertTriangle className="w-3 h-3" />Unpaid</div>;
      case 'trialing':
        return <div className="badge badge-info gap-1"><Clock className="w-3 h-3" />Trial</div>;
      default:
        return <div className="badge badge-neutral">{status}</div>;
    }
  };

  const formatPrice = (price: number, interval: string) => {
    return `€${price.toFixed(2)}/${interval === 'yearly' ? 'year' : 'month'}`;
  };

  const getDaysUntilExpiry = (endDate: string) => {
    const days = Math.ceil((new Date(endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    return days;
  };

  const columns: Column<SubscriptionWithDetails>[] = [
    {
      key: 'user.email',
      header: 'User',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.user.first_name && row.user.last_name && (
            <div className="text-sm opacity-70">{row.user.first_name} {row.user.last_name}</div>
          )}
        </div>
      )
    },
    {
      key: 'plan.display_name',
      header: 'Plan',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm opacity-70">
            {formatPrice(row.plan.price_eur, row.billing_cycle)}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      render: (value) => getStatusBadge(value)
    },
    {
      key: 'current_period_end',
      header: 'Expires',
      render: (value) => {
        const days = getDaysUntilExpiry(value);
        return (
          <div>
            <div className="font-medium">{new Date(value).toLocaleDateString()}</div>
            <div className={`text-sm ${days < 7 ? 'text-error' : days < 30 ? 'text-warning' : 'opacity-70'}`}>
              {days > 0 ? `${days} days left` : `${Math.abs(days)} days ago`}
            </div>
          </div>
        );
      }
    },
    {
      key: 'api_requests_used',
      header: 'Usage',
      render: (value, row) => {
        const percentage = row.plan.api_requests_per_month > 0 
          ? Math.round((value / row.plan.api_requests_per_month) * 100)
          : 0;
        return (
          <div>
            <div className="font-medium">{value.toLocaleString()}</div>
            <div className="text-sm opacity-70">
              {percentage}% of {row.plan.api_requests_per_month.toLocaleString()}
            </div>
          </div>
        );
      }
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  if (currentUser && currentUser.role !== 'ADMIN') {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <Shield className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Access Denied</h1>
              <p className="py-6 opacity-70">You don't have permission to access subscription management.</p>
              <a href="/dashboard" className="btn btn-primary">Go to Dashboard</a>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Subscription Management</h1>
            <p className="opacity-70 mt-2">
              Manage user subscriptions, billing, and plan changes
            </p>
          </div>
          
          <div className="stats shadow">
            <div className="stat">
              <div className="stat-figure text-primary">
                <CreditCard className="w-8 h-8" />
              </div>
              <div className="stat-title">Total Subscriptions</div>
              <div className="stat-value text-primary">{pagination.total}</div>
            </div>
          </div>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Filters */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Status</span>
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="past_due">Past Due</option>
                  <option value="canceled">Canceled</option>
                  <option value="unpaid">Unpaid</option>
                  <option value="trialing">Trial</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Plan</span>
                </label>
                <select
                  value={filters.planId}
                  onChange={(e) => setFilters(prev => ({ ...prev, planId: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="">All Plans</option>
                  {plans.map(plan => (
                    <option key={plan.id} value={plan.id}>{plan.display_name}</option>
                  ))}
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Expiring In</span>
                </label>
                <select
                  value={filters.expiringInDays}
                  onChange={(e) => setFilters(prev => ({ ...prev, expiringInDays: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="">Any Time</option>
                  <option value="7">7 Days</option>
                  <option value="30">30 Days</option>
                  <option value="90">90 Days</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Sort By</span>
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="select select-bordered"
                >
                  <option value="created_at">Created Date</option>
                  <option value="current_period_end">Expiry Date</option>
                  <option value="user_email">User Email</option>
                </select>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Order</span>
                </label>
                <select
                  value={filters.sortOrder}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as 'asc' | 'desc' }))}
                  className="select select-bordered"
                >
                  <option value="desc">Newest First</option>
                  <option value="asc">Oldest First</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Subscriptions Table */}
        <DataTable
          data={subscriptions || []}
          columns={columns}
          loading={loading}
          pagination={{
            page: pagination.page,
            limit: pagination.limit,
            total: pagination.total,
            onPageChange: (page) => setPagination(prev => ({ ...prev, page })),
            onLimitChange: (limit) => setPagination(prev => ({ ...prev, limit, page: 1 }))
          }}
          actions={(row) => (
            <button
              className="btn btn-ghost btn-sm"
              onClick={() => {
                setSelectedSubscription(row);
                setShowEditModal(true);
              }}
              title="Manage Subscription"
            >
              <Edit className="w-4 h-4" />
            </button>
          )}
          emptyMessage="No subscriptions found matching your criteria"
        />

        {/* Edit Subscription Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedSubscription(null);
          }}
          title="Manage Subscription"
          size="md"
        >
          {selectedSubscription && (
            <div className="space-y-4">
              <div className="card bg-base-200">
                <div className="card-body py-4">
                  <h4 className="font-semibold">Subscription Details</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="opacity-70">User:</span>
                      <div className="font-medium">{selectedSubscription.user.email}</div>
                    </div>
                    <div>
                      <span className="opacity-70">Plan:</span>
                      <div className="font-medium">{selectedSubscription.plan.display_name}</div>
                    </div>
                    <div>
                      <span className="opacity-70">Status:</span>
                      <div>{getStatusBadge(selectedSubscription.status)}</div>
                    </div>
                    <div>
                      <span className="opacity-70">Expires:</span>
                      <div className="font-medium">{new Date(selectedSubscription.current_period_end).toLocaleDateString()}</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Change Status</span>
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => handleUpdateSubscriptionStatus('active', 'Activated by admin')}
                    className="btn btn-success btn-sm"
                    disabled={actionLoading || selectedSubscription.status === 'active'}
                  >
                    Activate
                  </button>
                  <button
                    onClick={() => handleUpdateSubscriptionStatus('canceled', 'Canceled by admin')}
                    className="btn btn-error btn-sm"
                    disabled={actionLoading || selectedSubscription.status === 'canceled'}
                  >
                    Cancel
                  </button>
                </div>
              </div>

              <div className="modal-action">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedSubscription(null);
                  }}
                  className="btn btn-outline"
                  disabled={actionLoading}
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </DashboardLayout>
  );
}
