'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/auth-store';
import { apiClient, ApiKey } from '@/lib/api';
import DashboardLayout from '@/components/layout/DashboardLayout';
import DataTable, { Column } from '@/components/ui/DataTable';
import Modal, { ConfirmModal } from '@/components/ui/Modal';
import { Key, Plus, Eye, EyeOff, Copy, Trash2, Edit, AlertTriangle } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const createApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  allowed_ips: z.string().optional(),
  allowed_domains: z.string().optional(),
  expires_at: z.string().optional(),
});

type CreateApiKeyForm = z.infer<typeof createApiKeySchema>;

export default function ApiKeysPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore();
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSecretModal, setShowSecretModal] = useState(false);
  const [newApiKeySecret, setNewApiKeySecret] = useState<string>('');
  const [deleteConfirm, setDeleteConfirm] = useState<{ show: boolean; key: ApiKey | null }>({
    show: false,
    key: null
  });
  const [actionLoading, setActionLoading] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<CreateApiKeyForm>({
    resolver: zodResolver(createApiKeySchema)
  });

  useEffect(() => {
    fetchApiKeys();
  }, [authLoading, isAuthenticated]);

  const fetchApiKeys = async () => {
    // Don't fetch data if auth is still loading or user is not authenticated
    if (authLoading || !isAuthenticated) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const keys = await apiClient.getApiKeys();
      setApiKeys(keys);
    } catch (err: any) {
      console.error('Failed to fetch API keys:', err);
      setError(err.message || 'Failed to load API keys');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateApiKey = async (data: CreateApiKeyForm) => {
    try {
      const payload = {
        ...data,
        allowed_ips: data.allowed_ips ? data.allowed_ips.split(',').map(ip => ip.trim()) : undefined,
        allowed_domains: data.allowed_domains ? data.allowed_domains.split(',').map(domain => domain.trim()) : undefined,
        expires_at: data.expires_at || undefined,
      };

      const result = await apiClient.createApiKey(payload);
      setNewApiKeySecret(result.secret);
      setShowSecretModal(true);
      setShowCreateModal(false);
      reset();
      await fetchApiKeys();
    } catch (err: any) {
      console.error('Failed to create API key:', err);
      setError(err.message || 'Failed to create API key');
    }
  };

  const handleDeleteApiKey = async () => {
    if (!deleteConfirm.key) return;

    try {
      setActionLoading(true);
      await apiClient.deleteApiKey(deleteConfirm.key.id);
      await fetchApiKeys();
      setDeleteConfirm({ show: false, key: null });
    } catch (err: any) {
      console.error('Failed to delete API key:', err);
      setError(err.message || 'Failed to delete API key');
    } finally {
      setActionLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const columns: Column<ApiKey>[] = [
    {
      key: 'name',
      header: 'Name',
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          {row.description && (
            <div className="text-sm opacity-70">{row.description}</div>
          )}
        </div>
      )
    },
    {
      key: 'is_active',
      header: 'Status',
      render: (value) => (
        <div className={`badge ${value ? 'badge-success' : 'badge-error'}`}>
          {value ? 'Active' : 'Inactive'}
        </div>
      )
    },
    {
      key: 'total_requests',
      header: 'Total Requests',
      render: (value) => value?.toLocaleString() || '0'
    },
    {
      key: 'requests_this_month',
      header: 'This Month',
      render: (value) => value?.toLocaleString() || '0'
    },
    {
      key: 'last_used_at',
      header: 'Last Used',
      render: (value) => value ? new Date(value).toLocaleDateString() : 'Never'
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  if (error && !apiKeys.length) {
    return (
      <DashboardLayout>
        <div className="hero min-h-96">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <AlertTriangle className="w-16 h-16 mx-auto text-error mb-4" />
              <h1 className="text-2xl font-bold">Failed to Load API Keys</h1>
              <p className="py-6 opacity-70">{error}</p>
              <button 
                className="btn btn-primary"
                onClick={fetchApiKeys}
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">API Keys</h1>
            <p className="opacity-70 mt-2">
              Manage your API keys for accessing the Postal Terminal API
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn btn-primary"
          >
            <Plus className="w-4 h-4 mr-2" />
            Create API Key
          </button>
        </div>

        {error && (
          <div className="alert alert-error">
            <AlertTriangle className="w-5 h-5" />
            <span>{error}</span>
            <button 
              className="btn btn-sm btn-ghost"
              onClick={() => setError(null)}
            >
              Dismiss
            </button>
          </div>
        )}

        {/* API Keys Table */}
        <DataTable
          data={apiKeys}
          columns={columns}
          loading={loading}
          searchable
          searchPlaceholder="Search API keys..."
          emptyMessage="No API keys found. Create your first API key to get started."
          actions={(row) => (
            <>
              <button
                className="btn btn-ghost btn-sm"
                onClick={() => copyToClipboard(row.id)}
                title="Copy API Key ID"
              >
                <Copy className="w-4 h-4" />
              </button>
              <button
                className="btn btn-ghost btn-sm text-error"
                onClick={() => setDeleteConfirm({ show: true, key: row })}
                title="Delete API Key"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </>
          )}
        />

        {/* Create API Key Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New API Key"
          size="md"
        >
          <form onSubmit={handleSubmit(handleCreateApiKey)} className="space-y-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Name *</span>
              </label>
              <input
                {...register('name')}
                type="text"
                placeholder="My API Key"
                className={`input input-bordered ${errors.name ? 'input-error' : ''}`}
              />
              {errors.name && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.name.message}</span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Description</span>
              </label>
              <textarea
                {...register('description')}
                placeholder="Optional description for this API key"
                className={`textarea textarea-bordered ${errors.description ? 'textarea-error' : ''}`}
                rows={3}
              />
              {errors.description && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.description.message}</span>
                </label>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Allowed IPs</span>
              </label>
              <input
                {...register('allowed_ips')}
                type="text"
                placeholder="***********, ******** (comma separated)"
                className="input input-bordered"
              />
              <label className="label">
                <span className="label-text-alt">Leave empty to allow all IPs</span>
              </label>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Allowed Domains</span>
              </label>
              <input
                {...register('allowed_domains')}
                type="text"
                placeholder="example.com, api.example.com (comma separated)"
                className="input input-bordered"
              />
              <label className="label">
                <span className="label-text-alt">Leave empty to allow all domains</span>
              </label>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Expires At</span>
              </label>
              <input
                {...register('expires_at')}
                type="datetime-local"
                className="input input-bordered"
              />
              <label className="label">
                <span className="label-text-alt">Leave empty for no expiration</span>
              </label>
            </div>

            <div className="modal-action">
              <button
                type="button"
                onClick={() => setShowCreateModal(false)}
                className="btn btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting && <span className="loading loading-spinner loading-sm"></span>}
                Create API Key
              </button>
            </div>
          </form>
        </Modal>

        {/* API Key Secret Modal */}
        <Modal
          isOpen={showSecretModal}
          onClose={() => setShowSecretModal(false)}
          title="API Key Created Successfully"
          size="md"
        >
          <div className="space-y-4">
            <div className="alert alert-warning">
              <AlertTriangle className="w-5 h-5" />
              <div>
                <h3 className="font-bold">Important!</h3>
                <div className="text-sm">
                  This is the only time you'll see this API key. Make sure to copy it now.
                </div>
              </div>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">Your API Key</span>
              </label>
              <div className="join">
                <input
                  type="text"
                  value={newApiKeySecret}
                  readOnly
                  className="input input-bordered join-item flex-1 font-mono text-sm"
                />
                <button
                  onClick={() => copyToClipboard(newApiKeySecret)}
                  className="btn btn-outline join-item"
                >
                  <Copy className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="modal-action">
              <button
                onClick={() => setShowSecretModal(false)}
                className="btn btn-primary"
              >
                I've Copied the Key
              </button>
            </div>
          </div>
        </Modal>

        {/* Delete Confirmation Modal */}
        <ConfirmModal
          isOpen={deleteConfirm.show}
          onClose={() => setDeleteConfirm({ show: false, key: null })}
          onConfirm={handleDeleteApiKey}
          title="Delete API Key"
          message={`Are you sure you want to delete "${deleteConfirm.key?.name}"? This action cannot be undone.`}
          confirmText="Delete"
          type="error"
          loading={actionLoading}
        />
      </div>
    </DashboardLayout>
  );
}
